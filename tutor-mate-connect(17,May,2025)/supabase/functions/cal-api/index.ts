import { serve } from "https://deno.land/std@0.168.0/http/server.ts";

serve(async (req) => {
  
  if (req.method === "OPTIONS") {
    return new Response("OK", {
      headers: {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "POST, GET, OPTIONS",
        "Access-Control-Allow-Headers": "Content-Type, Authorization",
      },
    });
  }

  try {
    

    const { action, username } = await req.json();
    if (action !== "verifyUsername" || !username) {
      console.warn("Invalid request received:", { action, username });
      return new Response(JSON.stringify({ error: "Invalid request" }), {
        status: 400,
        headers: {
          "Content-Type": "application/json",
          "Access-Control-Allow-Origin": "*",
        },
      });
    }

    const calcomUrl = `https://cal.com/${username}`;
    

    const response = await fetch(calcomUrl, {
      method: "HEAD", // Faster than GET, checks if the page exists
    });

    if (response.ok) {
      
      return new Response(JSON.stringify({ exists: true }), {
        status: 200,
        headers: {
          "Content-Type": "application/json",
          "Access-Control-Allow-Origin": "*",
        },
      });
    } else {
      console.warn(`Username '${username}' does not exist`);
      return new Response(JSON.stringify({ exists: false }), {
        status: 404,
        headers: {
          "Content-Type": "application/json",
          "Access-Control-Allow-Origin": "*",
        },
      });
    }
  } catch (error) {
    console.error("Edge Function error:", error);
    return new Response(
      JSON.stringify({ error: "Failed to check Cal.com username", details: error.message }),
      {
        status: 500,
        headers: {
          "Content-Type": "application/json",
          "Access-Control-Allow-Origin": "*",
        },
      }
    );
  }
});

