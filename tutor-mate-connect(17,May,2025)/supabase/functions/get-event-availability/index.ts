import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";


const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Methods": "POST, OPTIONS",
  "Access-Control-Allow-Headers": "Content-Type, Authorization",
};


const supabaseUrl = Deno.env.get("SUPABASE_URL");
const supabaseKey = Deno.env.get("SUPABASE_SERVICE_ROLE_KEY");
const calendlyApiKey = Deno.env.get("CALENDLY_API");

if (!supabaseUrl || !supabaseKey || !calendlyApiKey) {
  throw new Error("Missing environment variables: SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY, or CALENDLY_API");
}

const supabase = createClient(supabaseUrl, supabaseKey);


serve(async (req) => {
  if (req.method === "OPTIONS") {
    return new Response(null, { status: 204, headers: corsHeaders });
  }

  if (req.method !== "POST") {
    return new Response(
      JSON.stringify({ error: "Method not allowed" }),
      { status: 405, headers: corsHeaders }
    );
  }

  try {
    const { event_uri, student_id } = await req.json();

    if (!event_uri || !student_id) {
      return new Response(
        JSON.stringify({ error: "Missing event_uri or student_id" }),
        { status: 400, headers: corsHeaders }
      );
    }

    
    const eventId = event_uri.split("/").pop();
    if (!eventId) {
      return new Response(
        JSON.stringify({ error: "Invalid event_uri format" }),
        { status: 400, headers: corsHeaders }
      );
    }

    const eventTypeUrl = `https://api.calendly.com/event_types/${eventId}`;
    const headers = {
      Authorization: `Bearer ${calendlyApiKey}`,
      "Content-Type": "application/json",
    };

    
    const eventRes = await fetch(eventTypeUrl, { headers });
    if (!eventRes.ok) {
      const errText = await eventRes.text();
      console.error(" Failed to fetch event type:", errText);
      return new Response(
        JSON.stringify({ error: "Failed to fetch Calendly event details" }),
        { status: 500, headers: corsHeaders }
      );
    }

    const eventJson = await eventRes.json();
    const eventName = eventJson?.resource?.name ?? "Unknown Event";

    
    const now = new Date();
    now.setMinutes(now.getMinutes() + 30);
    const startTime = now.toISOString();

    const end = new Date(now);
    end.setDate(end.getDate() + 7);
    const endTime = end.toISOString();

    const availabilityUrl = `https://api.calendly.com/event_type_available_times?event_type=https://api.calendly.com/event_types/${eventId}&start_time=${startTime}&end_time=${endTime}`;

    const availRes = await fetch(availabilityUrl, { headers });
    if (!availRes.ok) {
      const errText = await availRes.text();
      console.error(" Failed to fetch availability:", errText);
      return new Response(
        JSON.stringify({ error: "Failed to fetch Calendly availability" }),
        { status: 500, headers: corsHeaders }
      );
    }

    const availJson = await availRes.json();
    const calendlyTimes = availJson.collection || [];

    
    const { data: bookedSessions, error: sessionError } = await supabase
      .from("student_sessions")
      .select("start_time, event_name")
      .eq("student_id", student_id)
      .eq("event_name", eventName);

    if (sessionError) {
      console.error(" Supabase Error:", sessionError.message);
      return new Response(
        JSON.stringify({ error: "Failed to fetch student sessions" }),
        { status: 500, headers: corsHeaders }
      );
    }

    
    const filteredTimes = calendlyTimes.filter((slot) => {
      return !bookedSessions.some((session) => {
        return (
          new Date(session.start_time).toISOString() ===
            new Date(slot.start_time).toISOString() &&
          session.event_name === eventName
        );
      });
    });

   

    return new Response(
      JSON.stringify({
        event_name: eventName,
        times: filteredTimes,
      }),
      { status: 200, headers: corsHeaders }
    );
  } catch (err) {
    console.error(" Unexpected Error:", err);
    return new Response(
      JSON.stringify({
        error: "Internal Server Error",
        details: err.message,
      }),
      { status: 500, headers: corsHeaders }
    );
  }
});
