import { serve } from "https://deno.land/std@0.168.0/http/server.ts";

serve(async (req) => {
  
  if (req.method === "OPTIONS") {
    return new Response("OK", {
      headers: {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "POST, GET, OPTIONS",
        "Access-Control-Allow-Headers": "Content-Type, Authorization",
      },
    });
  }

  try {
    

    const { action, username } = await req.json();
    if (!username) {
      console.warn("Invalid request received:", { action, username });
      return new Response(JSON.stringify({ error: "Invalid request" }), {
        status: 400,
        headers: {
          "Content-Type": "application/json",
          "Access-Control-Allow-Origin": "*",
        },
      });
    }

    const calendlyUrl = `https://calendly.com/${username}`;

    if (action === "verifyUsername") {
      

      const response = await fetch(calendlyUrl, { method: "HEAD" });

      if (response.ok) {
        
        return new Response(JSON.stringify({ exists: true }), {
          status: 200,
          headers: {
            "Content-Type": "application/json",
            "Access-Control-Allow-Origin": "*",
          },
        });
      } else {
        console.warn(`Username '${username}' does not exist`);
        return new Response(JSON.stringify({ exists: false }), {
          status: 404,
          headers: {
            "Content-Type": "application/json",
            "Access-Control-Allow-Origin": "*",
          },
        });
      }
    }

    if (action === "fetchCalendlyURI") {
      

      return new Response(JSON.stringify({ uri: calendlyUrl }), {
        status: 200,
        headers: {
          "Content-Type": "application/json",
          "Access-Control-Allow-Origin": "*",
        },
      });
    }

    return new Response(JSON.stringify({ error: "Unknown action" }), {
      status: 400,
      headers: {
        "Content-Type": "application/json",
        "Access-Control-Allow-Origin": "*",
      },
    });

  } catch (error) {
    console.error("Edge Function error:", error);
    return new Response(
      JSON.stringify({ error: "Failed to process request", details: error }),
      {
        status: 500,
        headers: {
          "Content-Type": "application/json",
          "Access-Control-Allow-Origin": "*",
        },
      }
    );
  }
});