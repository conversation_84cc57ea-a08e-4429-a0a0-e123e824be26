import { serve } from "https://deno.land/std@0.140.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.39.5";
import { v4 as uuidv4 } from "https://esm.sh/uuid@9.0.1";

const supabaseUrl = Deno.env.get("SUPABASE_URL")!;
const supabaseKey = Deno.env.get("SUPABASE_SERVICE_ROLE_KEY")!;
const supabase = createClient(supabaseUrl, supabaseKey);

const CALENDLY_API = Deno.env.get("CALENDLY_API") || "";

const eventLogs: any[] = [];

async function fetchCalendlyDetails(eventUuid: string) {
  try {
    const url = `https://api.calendly.com/scheduled_events/${eventUuid}`;
    const response = await fetch(url, {
      method: "GET",
      headers: {
        Authorization: `Bearer ${CALENDLY_API}`,
        "Content-Type": "application/json",
      },
    });
    if (!response.ok) {
      throw new Error(`Failed to fetch Calendly details: ${response.statusText}`);
    }
    return await response.json();
  } catch (error) {
    console.error("Error fetching Calendly details:", error);
    return null;
  }
}

async function getStudentIdByEmail(email: string) {
  const { data, error } = await supabase
    .from("student_profiles")
    .select("id")
    .eq("email", email)
    .single();

  if (error || !data) {
    console.error(`Student not found for email: ${email}`);
    return null;
  }
  return data.id;
}

async function logEnrichedEvent(eventData: any) {
  const eventUuid = eventData.payload.scheduled_event?.uri?.split("/").pop();
  const studentEmail = eventData.payload.email;

  if (!eventUuid || !studentEmail) {
    console.error("Invalid payload: Missing event_uuid or student_email");
    return;
  }

  const studentId = await getStudentIdByEmail(studentEmail);
  if (!studentId) {
    console.error("No student ID found for email:", studentEmail);
    return;
  }

  

  const eventDetails = await fetchCalendlyDetails(eventUuid);
  if (!eventDetails) {
    console.error("No event details found for eventUuid:", eventUuid);
    return;
  }

  const enrichedEvent = {
    eventType: eventData.payload.scheduled_event.name,
    eventUuid,
    studentId,
    eventDetails: eventDetails?.resource || null,
  };

  
  await storeEnrichedEventToSupabase(enrichedEvent);
}

async function storeEnrichedEventToSupabase(enrichedEvent: any) {
  const event = enrichedEvent.eventDetails;

  if (!event) {
    console.error("Missing event details for Supabase insert.");
    return;
  }

  

  const { data: studentProfile, error: studentError } = await supabase
    .from("student_profiles")
    .select("id")
    .eq("id", enrichedEvent.studentId)
    .single();

  if (studentError || !studentProfile) {
    console.error(`Student not found for ID: ${enrichedEvent.studentId}`);
    return;
  }

  

  
  const startTimeUTC = event.start_time;
  const endTimeUTC = event.end_time;

  const dataToInsert = {
    id: uuidv4(),
    student_id: studentProfile.id,
    tutor_name: event.event_memberships[0]?.user_name || "",
    tutor_email: event.event_memberships[0]?.user_email || "",
    event_name: event.name || enrichedEvent.eventType,
    event_id: event.uuid || enrichedEvent.eventUuid,
    start_time: startTimeUTC,
    end_time: endTimeUTC,
    status: event.status,
    created_at: event.created_at,
    cancel_url: event.cancel_url || "", 
    scheduling_url: event.uri || "",
    meeting_url: event.location?.join_url || "",
    payment: "paid",
  };

  

  const { data, error } = await supabase.from("student_sessions").insert(dataToInsert);

  if (error) {
    console.error("Error inserting event into Supabase:", error.message);
    console.log(`[ERROR] Event storing failed: ${error.message}`);
  } else {
    console.log("Event stored successfully in student_sessions table:", data);
    console.log(`[SUCCESS] Event stored successfully!`);
  }
}

serve(async (req) => {
  console.log("Server received a request...");
  const headers = {
    "Content-Type": "application/json",
    "Access-Control-Allow-Origin": "*",
    "Access-Control-Allow-Methods": "POST, OPTIONS",
    "Access-Control-Allow-Headers": "Content-Type, Authorization",
  };

  if (req.method === "OPTIONS") {
    return new Response(null, { headers, status: 204 });
  }

  if (req.method === "GET") {
    return new Response(JSON.stringify({ events: eventLogs }), { headers, status: 200 });
  }

  if (req.method !== "POST") {
    return new Response(JSON.stringify({ error: "Method Not Allowed" }), {
      headers,
      status: 405,
    });
  }

  try {
    const body = await req.json();

    console.log("Received event:", body);

    if (!body.payload?.scheduled_event?.uri || !body.payload?.email) {
      console.error("Invalid payload: Missing event_uuid or student_email");
      return new Response(JSON.stringify({ error: "Invalid payload" }), { headers, status: 400 });
    }

    await logEnrichedEvent(body);

    return new Response(JSON.stringify({ message: "Data stored successfully" }), {
      headers,
      status: 200,
    });
  } catch (error) {
    console.error("Error processing webhook:", error);
    return new Response(JSON.stringify({ error: "Internal Server Error" }), {
      headers,
      status: 500,
    });
  }
});


