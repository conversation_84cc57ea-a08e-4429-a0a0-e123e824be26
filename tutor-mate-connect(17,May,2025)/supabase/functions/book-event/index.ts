import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";

const SUPABASE_URL = Deno.env.get("SUPABASE_URL");
const SUPABASE_SERVICE_ROLE_KEY = Deno.env.get("SUPABASE_SERVICE_ROLE_KEY");
const CAL_API_KEY = Deno.env.get("CALENDLY_API");

const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);


const CORS_HEADERS = {
  "Access-Control-Allow-Origin": "*", 
  "Access-Control-Allow-Methods": "OPTIONS, POST",
  "Access-Control-Allow-Headers": "Content-Type, Authorization, Accept", 
};

serve(async (req) => {
  
  if (req.method === "OPTIONS") {
    return new Response(null, { headers: CORS_HEADERS });
  }

  try {
    const { studentId, tutorId, tutorName, tutorEmail, eventName, slot, paymentId, eventUri } = await req.json();

    
    const { data: payment, error: paymentError } = await supabase
      .from("payments")
      .select("status")
      .eq("id", paymentId)
      .single();

    if (paymentError || !payment || payment.status !== "paid") {
      return new Response(JSON.stringify({ error: "Payment not verified." }), { status: 400, headers: CORS_HEADERS });
    }

    
    const calendlyBookingResponse = await fetch("https://api.calendly.com/scheduled_events", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${CAL_API_KEY}`,
      },
      body: JSON.stringify({
        "event": {
          "start_time": slot.start_time,
          "end_time": slot.end_time,
          "invitee": {
            "email": studentId, 
            "name": studentId,  
          },
          "event_type": eventUri, 
        },
      }),
    });

    const calendlyBookingData = await calendlyBookingResponse.json();
    if (!calendlyBookingResponse.ok) {
      return new Response(JSON.stringify({ error: "Failed to book on Calendly" }), { status: 400, headers: CORS_HEADERS });
    }

    
    const { error: insertError } = await supabase.from("student_sessions").insert({
      student_id: studentId,
      tutor_name: tutorName,
      tutor_email: tutorEmail,
      event_name: eventName,
      start_time: slot.start_time,
      end_time: slot.end_time,
      status: "confirmed",
      created_at: new Date().toISOString(),
      cancel_url: calendlyBookingData.cancel_url || null,
      meeting_url: calendlyBookingData.join_url || null, 
      event_id: calendlyBookingData.uri.split("/").pop(), 
      payment: paymentId,
    });

    if (insertError) {
      return new Response(JSON.stringify({ error: "Failed to store booking." }), { status: 400, headers: CORS_HEADERS });
    }

    return new Response(JSON.stringify({ success: true, message: "Booking confirmed!" }), { status: 200, headers: CORS_HEADERS });
  } catch (error) {
    return new Response(JSON.stringify({ error: "Internal server error." }), { status: 500, headers: CORS_HEADERS });
  }
});
