import { serve } from "https://deno.land/std@0.170.0/http/server.ts";
import Stripe from "https://esm.sh/stripe@11.18.0";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";
import { SubtleCryptoProvider } from "https://esm.sh/stripe?target=deno";

const stripeSecretKey = Deno.env.get("STRIPE_KEY");
const supabaseUrl = Deno.env.get("SUPABASE_URL");
const supabaseServiceRoleKey = Deno.env.get("SUPABASE_SERVICE_ROLE_KEY");
const stripeWebhookSecret = Deno.env.get("STRIPE_SIGNING_KEY");

if (!stripeSecretKey || !supabaseUrl || !supabaseServiceRoleKey || !stripeWebhookSecret) {
  throw new Error("Missing required environment variables");
}

const stripe = new Stripe(stripeSecretKey, { apiVersion: "2022-11-15" });
const supabase = createClient(supabaseUrl, supabaseServiceRoleKey);
const cryptoProvider = Stripe.createSubtleCryptoProvider();

serve(async (req: Request) => {
  if (req.method === "OPTIONS") {
    return new Response("ok", {
      headers: {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "POST, OPTIONS",
        "Access-Control-Allow-Headers": "Content-Type, Authorization",
      },
    });
  }

  if (req.method !== "POST") {
    return new Response("Method Not Allowed", { status: 405 });
  }

  const signature = req.headers.get("stripe-signature");
  const body = await req.text();

  let event;
  try {
    event = await stripe.webhooks.constructEventAsync(
      body,
      signature!,
      stripeWebhookSecret,
      undefined,
      cryptoProvider
    );
  } catch (err) {
    console.error("Webhook signature verification failed:", err);
    return new Response("Webhook signature verification failed", { status: 400 });
  }

  const eventType = event.type;

  try {
    switch (eventType) {
      case "checkout.session.completed": {
        const session = event.data.object;
        const metadata = session.metadata;

        if (!metadata) {
          return new Response("Missing metadata", { status: 400 });
        }

        let eventIdFromUri: string | null = null;
        if (metadata.eventUri) {
          try {
            const url = new URL(metadata.eventUri);
            const pathnameParts = url.pathname.split('/');
            eventIdFromUri = pathnameParts[pathnameParts.length - 1] || null;
            if (eventIdFromUri === "") {
              eventIdFromUri = null;
            }
          } catch (error) {
            console.error("Error parsing eventUri:", error);
          }
        }

        const studentId = metadata.studentId || metadata.student_id || null;
        const tutorId = metadata.tutor_id || null;
        const eventId = metadata.event_id || eventIdFromUri;
        const schedulingUrl = metadata.calendly_event_link || metadata.calendly_scheduling_url || null;

        if (!studentId) {
          return new Response("Missing studentId in metadata", { status: 400 });
        }

        const paymentData = {
          id: crypto.randomUUID(),
          created_at: new Date().toISOString(),
          amount: session.amount_total,
          currency: session.currency,
          status: session.payment_status,
          stripe_payment_intent: session.payment_intent,
          stripe_customer: session.customer || "unknown",
          event_id: eventId,
          student_id: studentId,
          payment_id: session.id,
          scheduling_url: schedulingUrl,
          tutor_id: tutorId
        };

        const { error: paymentError } = await supabase.from("payments").insert(paymentData);
        if (paymentError) {
          return new Response("Database insert failed", { status: 500 });
        }

        if (metadata.planId) {
          const { data: pendingSubscription, error: fetchError } = await supabase
            .from("student_subscriptions")
            .select("*")
            .eq("stripe_checkout_session_id", session.id)
            .single();

          if (fetchError) {
            console.error("Error fetching pending subscription:", fetchError);
          }

          const { data: existingSubscription, error: fetchError2 } = await supabase
            .from("student_subscriptions")
            .select("*")
            .eq("student_id", studentId)
            .eq("status", "active")
            .gt("remaining_sessions", 0)
            .maybeSingle();

          if (fetchError2) {
            return new Response("Database query failed", { status: 500 });
          }

          if (existingSubscription) {
            const updatedRemainingSession = existingSubscription.remaining_sessions + parseInt(metadata.planSessions);

            const { error: updateError } = await supabase
              .from("student_subscriptions")
              .update({
                remaining_sessions: updatedRemainingSession,
                updated_at: new Date().toISOString(),
                status: "active"
              })
              .eq("id", existingSubscription.id);

            if (updateError) {
              return new Response("Subscription update failed", { status: 500 });
            }
          } else {
            const subscriptionData = {
              id: crypto.randomUUID(),
              student_id: studentId,
              plan_id: parseInt(metadata.planId),
              start_date: new Date().toISOString(),
              end_date: new Date(new Date().setMonth(new Date().getMonth() + 1)).toISOString(),
              total_sessions: parseInt(metadata.planSessions),
              remaining_sessions: parseInt(metadata.planSessions),
              status: "active",
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString(),
            };

            const { error: subError } = await supabase
              .from("student_subscriptions")
              .insert(subscriptionData);

            if (subError) {
              return new Response("Subscription creation failed", { status: 500 });
            }
          }

          if (pendingSubscription) {
            const { error: deleteError } = await supabase
              .from("student_subscriptions")
              .delete()
              .eq("stripe_checkout_session_id", session.id);

            if (deleteError) {
              console.error("Error deleting pending subscription:", deleteError);
            }
          }
        } else if (metadata.student_id && metadata.tutor_id) {
          const { error } = await supabase.from('student_sessions').insert({
            student_id: metadata.student_id,
            tutor_id: metadata.tutor_id,
            calendly_event_type: metadata.event_type,
            calendly_scheduling_url: metadata.calendly_event_link,
            stripe_checkout_session_id: session.id,
            payment_status: 'paid',
          });

          if (error) {
            return new Response(`Supabase error: ${error.message}`, { status: 500 });
          }
        }

        break;
      }

      case "customer.subscription.created":
      case "customer.subscription.updated":
      case "customer.subscription.deleted": {
        const subscription = event.data.object;
        const stripeCustomerId = subscription.customer as string;

        const { data: student, error: fetchError } = await supabase
          .from("payments")
          .select("student_id")
          .eq("stripe_customer", stripeCustomerId)
          .order("created_at", { ascending: false })
          .limit(1)
          .single();

        if (fetchError || !student?.student_id) {
          return new Response("Student not found", { status: 404 });
        }

        const startDate = new Date(subscription.current_period_start * 1000).toISOString();
        const endDate = new Date(subscription.current_period_end * 1000).toISOString();
        const planId = subscription.items?.data?.[0]?.price?.id ?? null;

        const subscriptionRecord = {
          id: crypto.randomUUID(),
          student_id: student.student_id,
          plan_id: planId,
          start_date: startDate,
          end_date: endDate,
          total_sessions: 4,
          remaining_sessions: 4,
          status: subscription.status,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        };

        const { error: upsertError } = await supabase
          .from("student_subscriptions")
          .upsert(subscriptionRecord, { onConflict: "student_id" });

        if (upsertError) {
          return new Response("Subscription upsert failed", { status: 500 });
        }

        break;
      }

      case "payment_intent.succeeded": {
        const intent = event.data.object;
        break;
      }

      case "charge.succeeded": {
        const charge = event.data.object;
        break;
      }

      case "invoice.paid":
        break;

      case "invoice.payment_failed":
        break;

      case "payment_intent.payment_failed":
        break;

      case "payment_intent.canceled":
        break;

      case "transfer.paid": {
        const transfer = event.data.object;

        const transferId = transfer.id;
        const createdAt = new Date(transfer.created * 1000).toISOString();

        const { error } = await supabase
          .from('tutor_payouts')
          .update({ status: 'paid', paid_at: createdAt })
          .eq('transfer_id', transferId);

        if (error) {
          return new Response(`Supabase error: ${error.message}`, { status: 500 });
        }

        break;
      }

      case "account.updated": {
        const account = event.data.object;

        const { error } = await supabase
          .from('tutor_profiles')
          .update({
            is_onboarded:
              account.charges_enabled &&
              account.payouts_enabled &&
              account.details_submitted,
            charges_enabled: account.charges_enabled,
            payouts_enabled: account.payouts_enabled,
            details_submitted: account.details_submitted,
          })
          .eq('stripe_account_id', account.id);

        if (error) {
          return new Response(`Supabase error: ${error.message}`, { status: 500 });
        }

        break;
      }

      default:
        console.log(`Unhandled event type: ${eventType}`);
    }

    return new Response("Webhook received and processed", { status: 200 });
  } catch (error) {
    console.error("Webhook handler error:", error);
    return new Response("Internal server error", { status: 500 });
  }
});
