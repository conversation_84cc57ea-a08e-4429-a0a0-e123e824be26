import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";

const corsHeaders = {
  "Access-Control-Allow-Origin": "*", 
  "Access-Control-Allow-Methods": "POST, GET, OPTIONS",
  "Access-Control-Allow-Headers": "Content-Type, Authorization",
};

serve(async (req) => {
  if (req.method === "OPTIONS") {
    return new Response("ok", { headers: corsHeaders });
  }

  const supabase = createClient(
    Deno.env.get("SUPABASE_URL")!,
    Deno.env.get("SUPABASE_SERVICE_ROLE_KEY")!
  );

  try {
    const { tutor_id } = await req.json();
    if (!tutor_id) {
      return new Response(
        JSON.stringify({ error: "Tutor ID is required" }),
        { status: 400, headers: corsHeaders }
      );
    }

    
    const { data: tutor_Profiles, error: tutorError } = await supabase
      .from("tutor_profiles")
      .select("calendly_username")
      .eq("id", tutor_id)
      .single();

    if (tutorError || !tutor_Profiles?.calendly_username) {
      return new Response(
        JSON.stringify({ error: "Tutor profile not found or missing Calendly username" }),
        { status: 404, headers: corsHeaders }
      );
    }

    const calendlyApiKey = Deno.env.get("CALENDLY_API");
    if (!calendlyApiKey) {
      return new Response(
        JSON.stringify({ error: "Calendly API key not found" }),
        { status: 500, headers: corsHeaders }
      );
    }

    
    const userResponse = await fetch("https://api.calendly.com/users/me", {
      headers: {
        Authorization: `Bearer ${calendlyApiKey}`,
        "Content-Type": "application/json",
      },
    });

    if (!userResponse.ok) {
      return new Response(
        JSON.stringify({ error: "Failed to fetch Calendly user data" }),
        { status: userResponse.status, headers: corsHeaders }
      );
    }

    const userData = await userResponse.json();
    const userUri = userData?.resource?.uri;

    if (!userUri) {
      return new Response(
        JSON.stringify({ error: "User URI not found in Calendly response" }),
        { status: 500, headers: corsHeaders }
      );
    }

    
    const eventTypesResponse = await fetch(
      `https://api.calendly.com/event_types?user=${userUri}`,
      {
        headers: {
          Authorization: `Bearer ${calendlyApiKey}`,
          "Content-Type": "application/json",
        },
      }
    );

    if (!eventTypesResponse.ok) {
      return new Response(
        JSON.stringify({ error: "Failed to fetch event types" }),
        { status: eventTypesResponse.status, headers: corsHeaders }
      );
    }

    const eventTypes = await eventTypesResponse.json();

    return new Response(JSON.stringify(eventTypes), {
      headers: { "Content-Type": "application/json", ...corsHeaders },
    });
  } catch (error) {
    console.error("Error:", error);
    return new Response(
      JSON.stringify({ error: "Unexpected server error" }),
      { status: 500, headers: corsHeaders }
    );
  }
});
