
import { serve } from "https://deno.land/std@0.177.0/http/server.ts";
import Stripe from "https://esm.sh/stripe@12.5.0?target=deno";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.39.5";

// Environment variables
const stripeSecretKey = Deno.env.get("STRIPE_KEY");
const supabaseUrl = Deno.env.get("SUPABASE_URL");
const supabaseServiceKey = Deno.env.get("SUPABASE_SERVICE_ROLE_KEY");
const baseUrl =  "https://primetutors.net";

if (!stripeSecretKey || !supabaseUrl || !supabaseServiceKey) {
  throw new Error("Missing required environment variables");
}

const stripe = new Stripe(stripeSecretKey, {
  apiVersion: "2023-10-16"
});

const supabase = createClient(supabaseUrl, supabaseServiceKey);

serve(async (req: Request) => {
  // Handle CORS
  if (req.method === "OPTIONS") {
    return new Response("ok", {
      headers: {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "POST, OPTIONS",
        "Access-Control-Allow-Headers": "Content-Type, Authorization",
      },
    });
  }

  if (req.method !== "POST") {
    return new Response("Method Not Allowed", { status: 405 });
  }

  try {
    const body = await req.json();
    const { 
      tutorId, 
      tutorName, 
      studentEmail, 
      eventId, 
      studentId, 
      eventUri, 
      schedulingUrl, 
      amountCents,
      subjectName 
    } = body;

    console.log("Request payload:", body);

    // Validate required fields
    if (!tutorId || !tutorName || !studentEmail || !eventId || !studentId || !eventUri || !schedulingUrl) {
      return new Response(
        JSON.stringify({ 
          error: "Missing required fields", 
          receivedData: body 
        }), 
        {
          status: 400,
          headers: { 
            "Content-Type": "application/json",
            "Access-Control-Allow-Origin": "*" 
          },
        }
      );
    }

    // Default amount if not provided (e.g. $50)
    const sessionAmount = amountCents || 7000;

    // Fetch tutor's Stripe account ID
    const { data: tutorProfile, error: tutorError } = await supabase
      .from("tutor_profiles")
      .select("stripe_account_id")
      .eq("id", tutorId)
      .single();

    if (tutorError || !tutorProfile?.stripe_account_id) {
      console.error("Tutor profile fetch error:", tutorError);
      return new Response(
        JSON.stringify({ 
          error: "Tutor not connected to Stripe", 
          details: tutorError?.message || "No Stripe account found" 
        }), 
        {
          status: 400,
          headers: { 
            "Content-Type": "application/json",
            "Access-Control-Allow-Origin": "*" 
          },
        }
      );
    }

    // Create the Stripe checkout session
    const session = await stripe.checkout.sessions.create({
      payment_method_types: ["card"],
      mode: "payment",
      line_items: [
        {
          price_data: {
            currency: "usd",
            product_data: { 
              name: `1:1 ${subjectName || ""} Tutoring Session with ${tutorName}` 
            },
            unit_amount: sessionAmount,
          },
          quantity: 1,
        },
      ],
      payment_intent_data: {
        application_fee_amount: Math.round(sessionAmount * 0.3), // 30% platform fee
        transfer_data: {
          destination: tutorProfile.stripe_account_id,
        },
      },
      success_url: `${baseUrl}/student-dashboard?payment=success`,
      cancel_url: `${baseUrl}/student-dashboard?payment=cancel`,
      metadata: {
        student_id: studentId,
        tutor_id: tutorId,
        event_type: eventId,
        calendly_event_link: schedulingUrl,
        subject: subjectName || "General Tutoring"
      },
      client_reference_id: studentId,
    });

    return new Response(
      JSON.stringify({ url: session.url }), 
      {
        headers: { 
          "Content-Type": "application/json",
          "Access-Control-Allow-Origin": "*" 
        },
      }
    );
  } catch (error) {
    console.error("Stripe session creation failed:", error);
    return new Response(
      JSON.stringify({ 
        error: error || "Internal Server Error", 
        errorObject: JSON.stringify(error) 
      }), 
      {
        status: 500,
        headers: { 
          "Content-Type": "application/json",
          "Access-Control-Allow-Origin": "*" 
        },
      }
    );
  }
});
