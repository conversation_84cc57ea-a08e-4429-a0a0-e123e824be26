// supabase/functions/stripe-tutor-onboarding-webhook/index.ts
import { serve } from "https://deno.land/std@0.170.0/http/server.ts";
import Stripe from "https://esm.sh/stripe@11.18.0";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";
import { SubtleCryptoProvider } from "https://esm.sh/stripe?target=deno";

const stripeSecretKey = Deno.env.get("STRIPE_KEY");
const supabaseUrl = Deno.env.get("SUPABASE_URL");
const supabaseServiceRoleKey = Deno.env.get("SUPABASE_SERVICE_ROLE_KEY");
const stripeWebhookSecret = Deno.env.get("STRIPE_SIGNING_KEY");

if (!stripeSecretKey || !supabaseUrl || !supabaseServiceRoleKey || !stripeWebhookSecret) {
  throw new Error("Missing required environment variables");
}

const stripe = new Stripe(stripeSecretKey, { apiVersion: "2022-11-15" });
const supabase = createClient(supabaseUrl, supabaseServiceRoleKey);
const cryptoProvider = Stripe.createSubtleCryptoProvider();

serve(async (req: Request) => {
  if (req.method === "OPTIONS") {
    return new Response("ok", {
      headers: {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "POST, OPTIONS",
        "Access-Control-Allow-Headers": "Content-Type, Authorization",
      },
    });
  }

  if (req.method !== "POST") {
    return new Response("Method Not Allowed", { status: 405 });
  }

  const signature = req.headers.get("stripe-signature");
  const body = await req.text();

  let event;
  try {
    event = await stripe.webhooks.constructEventAsync(
      body,
      signature!,
      stripeWebhookSecret,
      undefined,
      cryptoProvider
    );
  } catch (err) {
    console.error("Webhook signature verification failed:", err);
    return new Response("Webhook signature verification failed", { status: 400 });
  }

  const eventType = event.type;
  console.log(`Processing Stripe webhook event: ${eventType}`);

  switch (eventType) {
    case "account.updated": {
      const account = event.data.object;

      if (account.details_submitted && account.charges_enabled) {
        console.log("Tutor completed onboarding:", account.id);

        // Update the tutor profile in Supabase
        const { error } = await supabase
          .from("tutor_profiles")
          .update({ onboarding_complete: true }) // Add this column if needed
          .eq("stripe_account_id", account.id);

        if (error) {
          console.error("Failed to update onboarding status:", error);
          return new Response("Supabase error", { status: 500 });
        }
      }
      break;
    }

    default:
      console.log(`Unhandled event type: ${eventType}`);
  }

  return new Response("Webhook received", { status: 200 });
});
