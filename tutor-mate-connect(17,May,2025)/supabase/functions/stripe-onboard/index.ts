
import { serve } from "https://deno.land/std@0.170.0/http/server.ts";
import Stripe from "https://esm.sh/stripe@11.18.0";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.39.7";

const stripeSecretKey = Deno.env.get("STRIPE_KEY");
const supabaseUrl = Deno.env.get("SUPABASE_URL");
const supabaseServiceRole = Deno.env.get("SUPABASE_SERVICE_ROLE_KEY");
const appBaseUrl = "https://primetutors.net";

if (!stripeSecretKey || !supabaseUrl || !supabaseServiceRole) {
  throw new Error("Missing required environment variables");
}

const stripe = new Stripe(stripeSecretKey, { apiVersion: "2023-10-16" });
const supabase = createClient(supabaseUrl, supabaseServiceRole);

serve(async (req: Request) => {
  const headers = {
    "Content-Type": "application/json",
    "Access-Control-Allow-Origin": "*",
    "Access-Control-Allow-Methods": "POST, OPTIONS",
    "Access-Control-Allow-Headers": "Content-Type, Authorization",
  };

  if (req.method === "OPTIONS") {
    return new Response(null, { status: 204, headers });
  }

  if (req.method !== "POST") {
    return new Response(JSON.stringify({ error: "Method not allowed" }), {
      status: 405,
      headers,
    });
  }

  try {
    const { tutor_id, email } = await req.json();

    if (!tutor_id || !email) {
      return new Response(
        JSON.stringify({ error: "Missing required parameters: tutor_id and email are required" }),
        { status: 400, headers }
      );
    }

    const { data: tutorProfile, error: fetchError } = await supabase
      .from("tutor_profiles")
      .select("stripe_account_id")
      .eq("id", tutor_id)
      .single();

    if (fetchError && fetchError.code !== "PGRST116") {
      console.error("Database query error:", fetchError);
      return new Response(JSON.stringify({ error: "Failed to retrieve tutor profile" }), {
        status: 500,
        headers,
      });
    }

    let stripeAccountId = tutorProfile?.stripe_account_id;
    let onboardingUrl = "";

    if (!stripeAccountId) {
      try {
        const account = await stripe.accounts.create({
          type: "express",
          country: "US", // Required to trigger verification
          email,
          capabilities: {
            card_payments: { requested: true },
            transfers: { requested: true },
          },
          business_type: "individual",
          settings: {
            payouts: {
              schedule: {
                interval: "manual",
              },
            },
          },
          // 👇 This helps trigger identity verification (including Photo ID)
          individual: {
            first_name: "First", // Replace with real data if collected
            last_name: "Last",
            email: email,
            dob: {
              day: 1,
              month: 1,
              year: 2000,
            },
            address: {
              line1: "123 Main St",
              city: "New York",
              state: "NY",
              postal_code: "10001",
              country: "US",
            },
          },
        });

        stripeAccountId = account.id;

        const { error: updateError } = await supabase
          .from("tutor_profiles")
          .update({ stripe_account_id: stripeAccountId })
          .eq("id", tutor_id);

        if (updateError) {
          console.error("Failed to update tutor profile:", updateError);
          return new Response(JSON.stringify({ error: "Failed to update tutor profile" }), {
            status: 500,
            headers,
          });
        }

        const accountLink = await stripe.accountLinks.create({
          account: stripeAccountId,
          refresh_url: `${appBaseUrl}/onboarding/refresh?tutor_id=${tutor_id}`,
          return_url: `${appBaseUrl}/tutor-dashboard/success?tutor_id=${tutor_id}`,
          type: "account_onboarding",
        });

        onboardingUrl = accountLink.url;
      } catch (stripeError) {
        console.error("Stripe account creation error:", stripeError);
        return new Response(JSON.stringify({ error: "Failed to create Stripe account" }), {
          status: 500,
          headers,
        });
      }
    }

    if (!onboardingUrl) {
      try {
        const loginLink = await stripe.accounts.createLoginLink(stripeAccountId);
        onboardingUrl = loginLink.url;
      } catch (linkError) {
        console.error("Login link generation error:", linkError);
        return new Response(JSON.stringify({ error: "Failed to generate login link" }), {
          status: 500,
          headers,
        });
      }
    }

    return new Response(JSON.stringify({ url: onboardingUrl }), {
      headers,
      status: 200,
    });
  } catch (error) {
    console.error("Unexpected error:", error);
    return new Response(JSON.stringify({ error: "Internal server error" }), {
      status: 500,
      headers,
    });
  }
});
