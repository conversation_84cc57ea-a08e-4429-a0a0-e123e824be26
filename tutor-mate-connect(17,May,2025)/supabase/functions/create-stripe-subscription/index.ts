import { serve } from "https://deno.land/std@0.170.0/http/server.ts";
import Stripe from "https://esm.sh/stripe@11.18.0";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.39.5";

const stripeSecretKey = Deno.env.get("STRIPE_KEY");
const supabaseUrl = Deno.env.get("SUPABASE_URL");
const supabaseAnonKey = Deno.env.get("SUPABASE_ANON_KEY");
const frontendUrl =  "https://primetutors.net";

if (!stripeSecretKey) throw new Error("STRIPE_SECRET_KEY not set.");
if (!supabaseUrl || !supabaseAnonKey) throw new Error("Supabase credentials not set.");

const stripe = new Stripe(stripeSecretKey, { apiVersion: "2022-11-15" });
const supabase = createClient(supabaseUrl, supabase<PERSON>nonKey);

serve(async (req: Request) => {
  if (req.method === "OPTIONS") {
    return new Response("ok", {
      headers: {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "POST, OPTIONS",
        "Access-Control-Allow-Headers": "Content-Type, Authorization",
      },
    });
  }

  if (req.method !== "POST") {
    return new Response("Method Not Allowed", { status: 405 });
  }

  try {
    const requestBody = await req.json();
    console.log("Received request body:", requestBody);
    
    const {
      studentEmail,
      studentId,
      planId,
      planName,
      planPriceId,
      planSessions,
    } = requestBody;

    if (!studentEmail || !studentId || !planId || !planPriceId) {
      console.error("Missing required fields:", { 
        studentEmail: !!studentEmail, 
        studentId: !!studentId, 
        planId: !!planId, 
        planPriceId: !!planPriceId
      });
      
      return new Response(
        JSON.stringify({ 
          error: "Missing required fields",
          received: requestBody
        }),
        {
          status: 400,
          headers: {
            "Content-Type": "application/json",
            "Access-Control-Allow-Origin": "*",
          },
        }
      );
    }

    // Create or retrieve Stripe customer
    let stripeCustomerId;
    const { data: existingCustomer } = await supabase
      .from("payments")
      .select("stripe_customer")
      .eq("student_id", studentId)
      .not("stripe_customer", "eq", "unknown")
      .order("created_at", { ascending: false })
      .limit(1);
    
    if (existingCustomer?.[0]?.stripe_customer) {
      stripeCustomerId = existingCustomer[0].stripe_customer;
    } else {
      const customer = await stripe.customers.create({
        email: studentEmail,
        metadata: { student_id: studentId }
      });
      stripeCustomerId = customer.id;
    }

    // Create Stripe checkout session
    const session = await stripe.checkout.sessions.create({
      mode: "payment",
      payment_method_types: ["card"],
      customer: stripeCustomerId,
      line_items: [{
        price_data: {
          currency: "usd",
          product_data: {
            name: planName,
            description: `${planSessions} tutoring sessions`,
          },
          unit_amount: parseInt(planPriceId) * 100,
        },
        quantity: 1,
      }],
      metadata: {
        studentId,
        planId,
        planSessions: String(planSessions),
      },
      success_url: `${frontendUrl}/student-dashboard?payment=success`,
      cancel_url: `${frontendUrl}/student-dashboard?payment=cancel`,
    });
    
    // Store pending subscription
    const subscriptionData = {
      student_id: studentId,
      plan_id: planId,
      total_sessions: planSessions,
      remaining_sessions: 0,
      status: "pending",
      created_at: new Date().toISOString(),
      stripe_checkout_session_id: session.id,
    };
      console.log("Inserting subscription data:", subscriptionData); 
    const { error: insertError } = await supabase
      .from("student_subscriptions")
      .insert(subscriptionData);

    if (insertError) {
      console.error("Supabase insert error:", insertError);
      return new Response(
        JSON.stringify({ 
          error: "Failed to insert subscription",
          details: insertError.message 
        }),
        {
          status: 500,
          headers: {
            "Content-Type": "application/json",
            "Access-Control-Allow-Origin": "*",
          },
        }
      );
    }
console.log("Subscription inserted successfully."); // Log success
    return new Response(JSON.stringify({ url: session.url }), {
      headers: {
        "Content-Type": "application/json",
        "Access-Control-Allow-Origin": "*",
      },
    });
  } catch (err) {
    console.error("Subscription creation error:", err);
    return new Response(
      JSON.stringify({ 
        error: "Subscription creation failed", 
        details: err
      }),
      {
        status: 500,
        headers: {
          "Content-Type": "application/json",
          "Access-Control-Allow-Origin": "*",
        },
      }
    );
  }
});