import { supabase } from "@/integrations/supabase/client";

export type Language = {
  id: number;
  name: string;
};

export const fetchLanguages = async (): Promise<Language[]> => {
  
  const { data, error } = await supabase
    .from('supported_languages')
    .select('*')
    .order('name');

  if (error) {
    console.error('Error fetching languages:', error);
    throw error;
  }
  
  
  return data || [];
};