export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: J<PERSON> | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      "Cal.com-api-table": {
        Row: {
          attrs: Json | null
        }
        Insert: {
          attrs?: Json | null
        }
        Update: {
          attrs?: Json | null
        }
        Relationships: []
      }
      messages: {
        Row: {
          content: string
          created_at: string
          id: string
          read: boolean | null
          recipient_id: string
          sender_id: string
          updated_at: string
        }
        Insert: {
          content: string
          created_at?: string
          id?: string
          read?: boolean | null
          recipient_id: string
          sender_id: string
          updated_at?: string
        }
        Update: {
          content?: string
          created_at?: string
          id?: string
          read?: boolean | null
          recipient_id?: string
          sender_id?: string
          updated_at?: string
        }
        Relationships: []
      }
      profiles: {
        Row: {
          availability: Json | null
          created_at: string
          email: string | null
          flexible_scheduling: boolean | null
          full_name: string | null
          hourly_rate: number | null
          id: string
          languages_spoken: string[] | null
          location: string | null
          max_students_per_session: number | null
          payment_method: string | null
          phone_number: string | null
          profile_completion_percentage: number | null
          profile_photo_url: string | null
          profile_status: string | null
          qualifications: Json | null
          role: string | null
          session_duration: string[] | null
          session_packages: Json | null
          specializations: string[] | null
          teaching_experience: string | null
          teaching_style: string | null
          timezone: string | null
          updated_at: string
          verification_documents: Json | null
        }
        Insert: {
          availability?: Json | null
          created_at?: string
          email?: string | null
          flexible_scheduling?: boolean | null
          full_name?: string | null
          hourly_rate?: number | null
          id: string
          languages_spoken?: string[] | null
          location?: string | null
          max_students_per_session?: number | null
          payment_method?: string | null
          phone_number?: string | null
          profile_completion_percentage?: number | null
          profile_photo_url?: string | null
          profile_status?: string | null
          qualifications?: Json | null
          role?: string | null
          session_duration?: string[] | null
          session_packages?: Json | null
          specializations?: string[] | null
          teaching_experience?: string | null
          teaching_style?: string | null
          timezone?: string | null
          updated_at?: string
          verification_documents?: Json | null
        }
        Update: {
          availability?: Json | null
          created_at?: string
          email?: string | null
          flexible_scheduling?: boolean | null
          full_name?: string | null
          hourly_rate?: number | null
          id?: string
          languages_spoken?: string[] | null
          location?: string | null
          max_students_per_session?: number | null
          payment_method?: string | null
          phone_number?: string | null
          profile_completion_percentage?: number | null
          profile_photo_url?: string | null
          profile_status?: string | null
          qualifications?: Json | null
          role?: string | null
          session_duration?: string[] | null
          session_packages?: Json | null
          specializations?: string[] | null
          teaching_experience?: string | null
          teaching_style?: string | null
          timezone?: string | null
          updated_at?: string
          verification_documents?: Json | null
        }
        Relationships: []
      }
      subjects: {
        Row: {
          id: number
          name: string
          created_at: string
        }
        Insert: {
          id?: number
          name: string
          created_at?: string
        }
        Update: {
          id?: number
          name?: string
          created_at?: string
        }
        Relationships: []
      }
      tutors_subject: {
        Row: {
          id: string
          tutor_id: string
          subjects_id: number
          created_at: string
        }
        Insert: {
          id?: string
          tutor_id: string
          subjects_id: number
          created_at?: string
        }
        Update: {
          id?: string
          tutor_id?: string
          subjects_id?: number
          created_at?: string
        }
        Relationships: [
          {
            foreignKeyName: 'tutor_subjects_tutor_id_fkey'
            columns: ['tutor_id']
            referencedRelation: 'profiles'
            referencedColumns: ['id']
          },
          {
            foreignKeyName: 'tutors_subjects_subjects_id_fkey'
            columns: ['subjects_id']
            referencedRelation: 'subjects'
            referencedColumns: ['id']
          }
        ]
      }
      calendly_accounts: {
        Row: {
          id: number
          created_at: string
          tutor_id: string
          access_token: string | null
          refresh_token: string | null
          calendly_user: string | null
          expires_at: string | null
          updated_at: string
        }
        Insert: {
          id?: number
          created_at?: string
          tutor_id: string
          access_token?: string | null
          refresh_token?: string | null
          calendly_user?: string | null
          expires_at?: string | null
          updated_at?: string
        }
        Update: {
          id?: number
          created_at?: string
          tutor_id?: string
          access_token?: string | null
          refresh_token?: string | null
          calendly_user?: string | null
          expires_at?: string | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: 'calendly_accounts_tutor_id_fkey'
            columns: ['tutor_id']
            referencedRelation: 'tutor_profiles'
            referencedColumns: ['id']
          }
        ]
      }
      
            
      student_profiles: {
        Row: {
          created_at: string
          email: string | null
          full_name: string | null
          grade_level: string | null
          id: string
          learning_goals: string | null
          learning_style: string | null
          location: string | null
          parent_info: Json | null
          phone_number: string | null
          preferred_languages: string[] | null
          profile_photo_url: string | null
          special_needs: string | null
          subjects: string[] | null
          timezone: string | null
          updated_at: string
        }
        Insert: {
          created_at?: string
          email?: string | null
          full_name?: string | null
          grade_level?: string | null
          id: string
          learning_goals?: string | null
          learning_style?: string | null
          location?: string | null
          parent_info?: Json | null
          phone_number?: string | null
          preferred_languages?: string[] | null
          profile_photo_url?: string | null
          special_needs?: string | null
          subjects?: string[] | null
          timezone?: string | null
          updated_at?: string
        }
        Update: {
          created_at?: string
          email?: string | null
          full_name?: string | null
          grade_level?: string | null
          id?: string
          learning_goals?: string | null
          learning_style?: string | null
          location?: string | null
          parent_info?: Json | null
          phone_number?: string | null
          preferred_languages?: string[] | null
          profile_photo_url?: string | null
          special_needs?: string | null
          subjects?: string[] | null
          timezone?: string | null
          updated_at?: string
        }
        Relationships: []
      }
      supported_languages: {
        Row: {
          created_at: string
          id: number
          name: string
        }
        Insert: {
          created_at?: string
          id?: number
          name: string
        }
        Update: {
          created_at?: string
          id?: number
          name?: string
        }
        Relationships: []
      }
      supported_subjects: {
        Row: {
          category: string
          created_at: string
          display_name: string
          id: number
          name: string
        }
        Insert: {
          category: string
          created_at?: string
          display_name: string
          id?: number
          name: string
        }
        Update: {
          category?: string
          created_at?: string
          display_name?: string
          id?: number
          name?: string
        }
        Relationships: []
      }
      tutor_languages: {
        Row: {
          created_at: string
          language_id: number
          tutor_id: string
        }
        Insert: {
          created_at?: string
          language_id: number
          tutor_id: string
        }
        Update: {
          created_at?: string
          language_id?: number
          tutor_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "tutor_languages_language_id_fkey"
            columns: ["language_id"]
            isOneToOne: false
            referencedRelation: "supported_languages"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "tutor_languages_tutor_id_fkey"
            columns: ["tutor_id"]
            isOneToOne: false
            referencedRelation: "tutor_profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      tutor_metrics: {
        Row: {
          active_students: number | null
          created_at: string
          earnings_month: number | null
          hours_taught: number | null
          id: string
          rating: number | null
          tutor_id: string
          upcoming_sessions: number | null
          updated_at: string
        }
        Insert: {
          active_students?: number | null
          created_at?: string
          earnings_month?: number | null
          hours_taught?: number | null
          id?: string
          rating?: number | null
          tutor_id: string
          upcoming_sessions?: number | null
          updated_at?: string
        }
        Update: {
          active_students?: number | null
          created_at?: string
          earnings_month?: number | null
          hours_taught?: number | null
          id?: string
          rating?: number | null
          tutor_id?: string
          upcoming_sessions?: number | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "tutor_metrics_tutor_id_fkey"
            columns: ["tutor_id"]
            isOneToOne: true
            referencedRelation: "tutor_profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      payments: {
        Row: {
          id: string;
          created_at: string;
          amount: number;
          currency: string;
          status: string;
          stripe_payment_intent: string;
          stripe_customer: string;
          event_id: string;
          student_id: string;
          tutor_id: string;
          payment_id: string;
          scheduling_url: string;
        };
        Insert: {
          id?: string;
          created_at?: string;
          amount: number;
          currency: string;
          status?: string;
          stripe_payment_intent: string;
          stripe_customer: string;
          event_id: string;
          student_id: string;
          tutor_id: string;
          payment_id: string;
          scheduling_url: string;
        };
        Update: {
          id?: string;
          created_at?: string;
          amount?: number;
          currency?: string;
          status?: string;
          stripe_payment_intent?: string;
          stripe_customer?: string;
          event_id?: string;
          student_id?: string;
          tutor_id?: string;
          payment_id?: string;
          scheduling_url?: string;
        };
        Relationships: [
          {
            foreignKeyName: "payments_student_id_fkey";
            columns: ["student_id"];
            isOneToOne: false;
            referencedRelation: "student_profiles";
            referencedColumns: ["id"];
          }
        ];
      };
      
<<<<<<< HEAD
      subscription_plan: {
        Row: {
          id: number;  // Use number since `int2` will be stored as a 2-byte integer
          created_at: string;
          name: string;
          description: string;
          price: number;
          sessions_count: number;
          updated_at: string;
        };
        Insert: {
          id?: number;  // Use number for inserting `int2`
          created_at?: string;
          name: string;
          description: string;
          price: number;
          sessions_count: number;
          updated_at?: string;
        };
        Update: {
          id?: number;  // Use number for updating `int2`
          created_at?: string;
          name?: string;
          description?: string;
          price?: number;
          sessions_count?: number;
          updated_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: "subscription_plan_id_fkey";
            columns: ["id"];
            isOneToOne: true;
            referencedRelation: "some_other_table";  // Replace with actual reference if needed
            referencedColumns: ["id"];
          }
        ];
      };

      student_subscriptions: {
        Row: {
          id: string;
          created_at: string;
          updated_at: string;
          student_id: string;
          plan_id: bigint;
          start_date: string;
          end_date: string;
          total_sessions: number;
          remaining_sessions: number;
          status: string;
          stripe_checkout_session_id: string; // Added this field
          
        };
        Insert: {
          id?: string;
          created_at?: string;
          updated_at?: string;
          student_id: string;
          plan_id: bigint;
          start_date: string;
          end_date: string;
          total_sessions: number;
          remaining_sessions: number;
          status: string;
          stripe_checkout_session_id: string; // Added this field
          
        };
        Update: {
          id?: string;
          created_at?: string;
          updated_at?: string;
          student_id?: string;
          plan_id?: bigint;
          start_date?: string;
          end_date?: string;
          total_sessions?: number;
          remaining_sessions?: number;
          status?: string;
          stripe_checkout_session_id?: string; // Added this field
          
        };
        Relationships: [
          {
            foreignKeyName: "student_subscriptions_student_id_fkey";
            columns: ["student_id"];
            isOneToOne: true;
            referencedRelation: "student_profiles";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "student_subscriptions_plan_id_fkey";
            columns: ["plan_id"];
            isOneToOne: true;
            referencedRelation: "subscription_plan";
            referencedColumns: ["id"];
          }
        ];
      };
      

      subscription_sessions_used: {
        Row: {
          id: bigint;
          created_at: string;
          updated_at: string;
          subscription_id: bigint;
          session_id: string;
          used: boolean;
        };
        Insert: {
          id?: bigint;
          created_at?: string;
          updated_at?: string;
          subscription_id: bigint;
          session_id: string;
          used: boolean;
        };
        Update: {
          id?: bigint;
          created_at?: string;
          updated_at?: string;
          subscription_id?: bigint;
          session_id?: string;
          used?: boolean;
        };
        Relationships: [
          {
            foreignKeyName: "subscription_sessions_used_subscription_id_fkey";
            columns: ["subscription_id"];
            isOneToOne: true;
            referencedRelation: "student_subscriptions";
            referencedColumns: ["id"];
          }
        ];
      };
      
=======
>>>>>>> 1eb2101125be0e0c03aa48ce278f888abfb8d708
      
      student_sessions: {
        Row: {
          id: string;
          student_id: string;
          tutor_name: string;
          tutor_email: string;
          event_name: string;
          event_id: string;
          start_time: string;
          end_time: string;
          status: string;
          created_at: string;
          cancel_url: string;
          meeting_url: string;
          scheduling_url?: string;
          payment: string; 
        };
        Insert: {
          id?: string;
          student_id: string;
          tutor_name: string;
          tutor_email: string;
          event_name: string;
          event_id: string;
          start_time: string;
          end_time: string;
          status?: string;
          created_at?: string;
          cancel_url?: string;
          meeting_url?: string;
          scheduling_url?: string;
          payment: string; 
        };
        Update: {
          id?: string;
          student_id?: string;
          tutor_name?: string;
          tutor_email?: string;
          event_name?: string;
          event_id?: string;
          start_time?: string;
          end_time?: string;
          status?: string;
          created_at?: string;
          cancel_url?: string;
          meeting_url?: string;
          scheduling_url?: string;
          payment?: string; 
        };
        Relationships: [
          {
            foreignKeyName: "student_sessions_student_id_fkey";
            columns: ["student_id"];
            isOneToOne: false;
            referencedRelation: "student_profiles";
            referencedColumns: ["id"];
          }
        ];
      }
      
      
    tutor_sessions : {
        Row: {
          id: string; 
          created_at: string; 
          start_time: string; 
          end_time: string; 
          event_name: string; 
          join_url: string | null; 
          cancel_url: string | null; 
          status: string; 
          tutor_id: string; 
        };
        Insert: {
          id?: string;
          created_at?: string;
          start_time: string;
          end_time: string;
          event_name: string;
          join_url?: string;
          cancel_url?: string;
          status?: string;
          tutor_id: string;
        };
        Update: {
          id?: string;
          created_at?: string;
          start_time?: string;
          end_time?: string;
          event_name?: string;
          join_url?: string;
          cancel_url?: string;
          status?: string;
          tutor_id?: string;
        };
        Relationships: [
          {
            foreignKeyName: "tutor_sessions_tutor_id_fkey";
            columns: ["tutor_id"];
            referencedRelation: "users"; 
            referencedColumns: ["id"];
          }
        ];
      };
<<<<<<< HEAD

      student_payments: {
        Row: {
          id: string; // uuid
          student_id: string; // uuid
          tutor_id: string; // uuid
          event_type_id: string; // text
          payment_id: string; // text
          amount: number; // numeric
          status: string; // text
          payload: any; // jsonb
          scheduling_url: string; // text
          booked_event_id: string; // text
          created_at: string; // timestamptz
          subject: string; // text
          calendly_event_link: string; // text
        };
        Insert: {
          id?: string;
          student_id: string;
          tutor_id: string;
          event_type_id: string;
          payment_id: string;
          amount: number;
          status?: string;
          payload?: any;
          scheduling_url?: string;
          booked_event_id?: string;
          created_at?: string;
          subject?: string;
          calendly_event_link?: string;
        };
        Update: {
          id?: string;
          student_id?: string;
          tutor_id?: string;
          event_type_id?: string;
          payment_id?: string;
          amount?: number;
          status?: string;
          payload?: any;
          scheduling_url?: string;
          booked_event_id?: string;
          created_at?: string;
          subject?: string;
          calendly_event_link?: string;
        };
        Relationships: [
          {
            foreignKeyName: "student_payments_student_id_fkey";
            columns: ["student_id"];
            referencedRelation: "student_profiles";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "student_payments_tutor_id_fkey";
            columns: ["tutor_id"];
            referencedRelation: "tutor_profiles";
            referencedColumns: ["id"];
          }
        ];
      };
      
     
=======
>>>>>>> 1eb2101125be0e0c03aa48ce278f888abfb8d708
      
      
      
      tutor_profiles: {
        Row: {
          availability: Json | null
          bio: string | null
          cal_username: string | null
          calendly_username: string | null
          created_at: string
          email: string | null
          flexible_scheduling: boolean | null
          full_name: string | null
          hourly_rate: number | null
          id: string
          is_verified: boolean | null
          location: string | null
          max_students_per_session: number | null
          payment_method: string | null
          phone_number: string | null
          profile_completion_percentage: number | null
          profile_photo_url: string | null
          profile_status: string | null
          qualifications: Json | null
          search_vector: unknown | null
          stripe_account_id?:string | null
          session_duration: string[] | null
          session_packages: Json | null
          teaching_experience: string | null
          teaching_style: string | null
          timezone: string | null
          updated_at: string
          verification_documents: Json | null
        }
        Insert: {
          availability?: Json | null
          bio?: string | null
          cal_username?: string | null
          calendly_username?: string | null
          created_at?: string
          email?: string | null
          flexible_scheduling?: boolean | null
          full_name?: string | null
          hourly_rate?: number | null
          id: string
          is_verified?: boolean | null
          location?: string | null
          max_students_per_session?: number | null
          payment_method?: string | null
          phone_number?: string | null
          profile_completion_percentage?: number | null
          profile_photo_url?: string | null
          profile_status?: string | null
          stripe_account_id?:string | null
          qualifications?: Json | null
          search_vector?: unknown | null
          session_duration?: string[] | null
          session_packages?: Json | null
          teaching_experience?: string | null
          teaching_style?: string | null
          timezone?: string | null
          updated_at?: string
          verification_documents?: Json | null
        }
        Update: {
          availability?: Json | null
          bio?: string | null
          cal_username?: string | null
          calendly_username?: string | null
          created_at?: string
          email?: string | null
          flexible_scheduling?: boolean | null
          full_name?: string | null
          hourly_rate?: number | null
          id?: string
          is_verified?: boolean | null
          location?: string | null
          max_students_per_session?: number | null
          payment_method?: string | null
          phone_number?: string | null
          stripe_account_id?:string | null
          profile_completion_percentage?: number | null
          profile_photo_url?: string | null
          profile_status?: string | null
          qualifications?: Json | null
          search_vector?: unknown | null
          session_duration?: string[] | null
          session_packages?: Json | null
          teaching_experience?: string | null
          teaching_style?: string | null
          timezone?: string | null
          updated_at?: string
          verification_documents?: Json | null
        }
        Relationships: []
      }


      tutor_subjects: {
        Row: {
          created_at: string
          subject_id: number
          tutor_id: string
        }
        Insert: {
          created_at?: string
          subject_id: number
          tutor_id: string
        }
        Update: {
          created_at?: string
          subject_id?: number
          tutor_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "tutor_subjects_subject_id_fkey"
            columns: ["subject_id"]
            isOneToOne: false
            referencedRelation: "supported_subjects"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "tutor_subjects_tutor_id_fkey"
            columns: ["tutor_id"]
            isOneToOne: false
            referencedRelation: "tutor_profiles"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      verify_calendar_username: {
        Args: {
          username: string
        }
        Returns: boolean
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type PublicSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  PublicTableNameOrOptions extends
    | keyof (PublicSchema["Tables"] & PublicSchema["Views"])
    | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
    ? keyof (Database[PublicTableNameOrOptions["schema"]]["Tables"] &
        Database[PublicTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? (Database[PublicTableNameOrOptions["schema"]]["Tables"] &
      Database[PublicTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : PublicTableNameOrOptions extends keyof (PublicSchema["Tables"] &
        PublicSchema["Views"])
    ? (PublicSchema["Tables"] &
        PublicSchema["Views"])[PublicTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  PublicTableNameOrOptions extends
    | keyof PublicSchema["Tables"]
    | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
    ? keyof Database[PublicTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? Database[PublicTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : PublicTableNameOrOptions extends keyof PublicSchema["Tables"]
    ? PublicSchema["Tables"][PublicTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  PublicTableNameOrOptions extends
    | keyof PublicSchema["Tables"]
    | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
    ? keyof Database[PublicTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? Database[PublicTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : PublicTableNameOrOptions extends keyof PublicSchema["Tables"]
    ? PublicSchema["Tables"][PublicTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  PublicEnumNameOrOptions extends
    | keyof PublicSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends PublicEnumNameOrOptions extends { schema: keyof Database }
    ? keyof Database[PublicEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = PublicEnumNameOrOptions extends { schema: keyof Database }
  ? Database[PublicEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : PublicEnumNameOrOptions extends keyof PublicSchema["Enums"]
    ? PublicSchema["Enums"][PublicEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof PublicSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof PublicSchema["CompositeTypes"]
    ? PublicSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never
