import { useEffect, useState } from "react";
import { useN<PERSON><PERSON>, Link } from "react-router-dom";
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useToast } from "@/hooks/use-toast";
import { Eye, EyeOff } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";

const TutorSignup = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const [showPassword, setShowPassword] = useState(false);
  const [name, setName] = useState("");
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    localStorage.setItem("signupRoute", "/signup-tutor");
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!name || !email || !password) {
      toast({ title: "Error", description: "Please fill in all fields", variant: "destructive" });
      return;
    }

    setLoading(true);
    const { error } = await supabase.auth.signUp({
      email,
      password,
      options: { data: { full_name: name, role: "tutor" } },
    });

    if (error) {
      toast({ title: "Error", description: error.message, variant: "destructive" });
    } else {
      localStorage.setItem("userRole", "tutor");
      toast({ title: "Sign Up Successful", description: "Check your email to verify your account." });
      navigate("/build-profile");
    }
    setLoading(false);
  };

  const handleGoogleSignup = async () => {
    localStorage.setItem("userRole", "tutor");

    const { error } = await supabase.auth.signInWithOAuth({
      provider: "google",
      options: { redirectTo: `${window.location.origin}/auth/v1/callback` },
    });

    if (error) {
      toast({ title: "Error", description: error.message, variant: "destructive" });
    }
  };

  return (
    <div className="flex min-h-screen items-center justify-center bg-background p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="space-y-2">
          <CardTitle className="text-2xl font-bold">Sign up as a tutor</CardTitle>
          <p className="text-sm text-muted-foreground">
            Already have an account? <Link to="/login" className="text-sage-600 hover:underline">Log in</Link>
          </p>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Button variant="outline" className="w-full" onClick={handleGoogleSignup} disabled={loading}>
              Continue with Google
            </Button>
            <div className="relative flex justify-center text-xs uppercase">
              <span className="bg-background px-2 text-muted-foreground">or</span>
              <div className="absolute inset-x-0 top-1/2 h-px -translate-y-1/2 bg-muted"></div>
            </div>

            <form onSubmit={handleSubmit} className="space-y-4">
              <Input placeholder="Your name" value={name} onChange={(e) => setName(e.target.value)} disabled={loading} />
              <Input type="email" placeholder="Your email" value={email} onChange={(e) => setEmail(e.target.value)} disabled={loading} />
              <div className="relative">
                <Input
                  type={showPassword ? "text" : "password"}
                  placeholder="Your password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  disabled={loading}
                />
                <button type="button" onClick={() => setShowPassword(!showPassword)} className="absolute right-3 top-1/2 -translate-y-1/2" disabled={loading}>
                  {showPassword ? <EyeOff className="h-4 w-4 text-gray-500" /> : <Eye className="h-4 w-4 text-gray-500" />}
                </button>
              </div>

              <Button type="submit" className="w-full bg-sage-600 hover:bg-sage-700" disabled={loading}>
                {loading ? "Signing up..." : "Sign up"}
              </Button>
            </form>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default TutorSignup;
