import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";

const Terms = () => {
  return (
    <div className="container mx-auto px-4 py-8">
      <Card>
        <CardHeader>
          <CardTitle className="text-2xl font-bold">Terms of Use</CardTitle>
        </CardHeader>
        <CardContent className="prose max-w-none">
          <h2>1. Acceptance of Terms</h2>
          <p>By accessing and using this platform, you agree to be bound by these Terms of Use.</p>

          <h2>2. User Accounts</h2>
          <p>Users must maintain accurate account information and are responsible for all activities under their accounts.</p>

          <h2>3. Platform Rules</h2>
          <ul>
            <li>Users must be respectful to others</li>
            <li>No sharing of account credentials</li>
            <li>No misuse of the platform</li>
          </ul>

          <h2>4. Content Guidelines</h2>
          <p>Users are responsible for the content they share and must ensure it does not violate any laws or regulations.</p>

          <h2>5. Termination</h2>
          <p>We reserve the right to terminate or suspend accounts that violate these terms.</p>
        </CardContent>
      </Card>
    </div>
  );
};

export default Terms;