import { Card } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Download, Mail } from "lucide-react";

const Press = () => {
  const pressReleases = [
    {
      title: "Prime Tutors Launches New AI-Powered Learning Features",
      date: "March 15, 2024",
      excerpt: "Leading online tutoring platform introduces innovative AI features to enhance personalized learning experience.",
    },
    {
      title: "Prime Tutors Reaches 100,000 Student Milestone",
      date: "February 1, 2024",
      excerpt: "Platform celebrates helping 100,000 students achieve their academic goals through personalized tutoring.",
    },
    {
      title: "Prime Tutors Expands Global Reach with New Languages",
      date: "January 10, 2024",
      excerpt: "Platform now offers tutoring services in 10 different languages to serve a broader international student base.",
    },
  ];

  return (
    <div className="min-h-screen bg-sage-50 py-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-sage-800 mb-4">Press Center</h1>
          <p className="text-lg text-sage-600 max-w-2xl mx-auto">
            Get the latest news and updates about Prime Tutors' mission to transform education through personalized tutoring.
          </p>
        </div>

        <div className="grid gap-8 mb-12">
          {pressReleases.map((release, index) => (
            <Card key={index} className="p-6">
              <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
                <div>
                  <h3 className="text-xl font-semibold text-sage-800 mb-2">{release.title}</h3>
                  <p className="text-sage-500 text-sm mb-2">{release.date}</p>
                  <p className="text-sage-600">{release.excerpt}</p>
                </div>
                <Button variant="outline" className="whitespace-nowrap">
                  Read More
                </Button>
              </div>
            </Card>
          ))}
        </div>

        <div className="grid md:grid-cols-2 gap-8">
          <Card className="p-6">
            <h2 className="text-xl font-semibold text-sage-800 mb-4">Media Kit</h2>
            <p className="text-sage-600 mb-6">
              Download our media kit for company information, logos, and brand guidelines.
            </p>
            <Button className="w-full sm:w-auto">
              Download Media Kit
              <Download className="ml-2 h-4 w-4" />
            </Button>
          </Card>

          <Card className="p-6">
            <h2 className="text-xl font-semibold text-sage-800 mb-4">Media Inquiries</h2>
            <p className="text-sage-600 mb-6">
              For press inquiries, please contact our media relations team.
            </p>
            <Button variant="outline" className="w-full sm:w-auto">
              Contact Press Team
              <Mail className="ml-2 h-4 w-4" />
            </Button>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default Press;