import { Card } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Mail } from "lucide-react";

const Careers = () => {
  const openPositions = [
    {
      title: "Senior Full Stack Developer",
      department: "Engineering",
      location: "Remote",
      type: "Full-time",
    },
    {
      title: "Education Content Specialist",
      department: "Content",
      location: "Hybrid",
      type: "Full-time",
    },
    {
      title: "Customer Success Manager",
      department: "Operations",
      location: "Remote",
      type: "Full-time",
    },
  ];

  return (
    <div className="min-h-screen bg-sage-50 py-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-sage-800 mb-4">Join Our Team</h1>
          <p className="text-lg text-sage-600 max-w-2xl mx-auto">
            Help us transform education by connecting students with exceptional tutors. 
            We're looking for passionate individuals to join our mission.
          </p>
        </div>

        <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3 mb-12">
          {openPositions.map((position, index) => (
            <Card key={index} className="p-6">
              <h3 className="text-xl font-semibold text-sage-800 mb-2">{position.title}</h3>
              <div className="space-y-2 text-sage-600 mb-4">
                <p>Department: {position.department}</p>
                <p>Location: {position.location}</p>
                <p>Type: {position.type}</p>
              </div>
              <Button className="w-full">
                Apply Now
                <Mail className="ml-2 h-4 w-4" />
              </Button>
            </Card>
          ))}
        </div>

        <div className="text-center">
          <h2 className="text-2xl font-semibold text-sage-800 mb-4">Don't see the right role?</h2>
          <p className="text-sage-600 mb-6">
            We're always looking for talented individuals. Send us your resume and we'll keep you in mind for future opportunities.
          </p>
          <Button variant="outline" className="inline-flex items-center">
            Send General Application
            <Mail className="ml-2 h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );
};

export default Careers;