import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { useToast } from "@/components/ui/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { TutorProfileView } from "@/components/profile/TutorProfileView";
import { StudentProfileView } from "@/components/profile/StudentProfileView";

type UserRole = "student" | "tutor" | null;

const Profile = () => {
  const { toast } = useToast();
  const [loading, setLoading] = useState(true);
  const [userRole, setUserRole] = useState<UserRole>(null);

  useEffect(() => {
    fetchUserProfile();
  }, []);

  const fetchUserProfile = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user || !user.email) throw new Error("No user found");

      const userEmail = user.email;

      
      const { data: profileData } = await supabase
        .from("profiles")
        .select("role")
        .eq("email", userEmail)
        .maybeSingle();

      if (profileData?.role) {
        setUserRole(profileData.role as UserRole);
        return;
      }

      
      const { data: studentData } = await supabase
        .from("student_profiles")
        .select("id")
        .eq("email", userEmail)
        .maybeSingle();

      if (studentData) {
        setUserRole("student");
        return;
      }

      
      const { data: tutorData } = await supabase
        .from("tutor_profiles")
        .select("id")
        .eq("email", userEmail)
        .maybeSingle();

      if (tutorData) {
        setUserRole("tutor");
        return;
      }

      throw new Error("User profile not found in any table");

    } catch (error) {
      console.error("Error fetching profile:", error);
      toast({
        title: "Error",
        description: "Failed to load profile data",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-background p-6">
        <div className="max-w-4xl mx-auto">
          <Card>
            <CardContent className="p-6 text-muted-foreground">
              Loading profile data...
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }
  

  return (
    <div className="min-h-screen bg-background p-6">
      <div className="mx-auto max-w-4xl space-y-6">
        {userRole === "tutor" ? (
          <TutorProfileView />
        ) : userRole === "student" ? (
          <StudentProfileView />
        ) : (
          <div className="text-center text-red-500">User profile not found.</div>
        )}
      </div>
    </div>
  );
};

export default Profile;
