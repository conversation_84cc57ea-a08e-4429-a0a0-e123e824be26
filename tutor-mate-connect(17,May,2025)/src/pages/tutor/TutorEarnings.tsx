import { Card } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
} from "recharts";

const earningsData = [
  { week: "Week 1", earnings: 450 },
  { week: "Week 2", earnings: 580 },
  { week: "Week 3", earnings: 620 },
  { week: "Week 4", earnings: 790 },
];

const TutorEarnings = () => {
  const monthlyGoal = 2500;
  const currentEarnings = 2040;
  const progressPercentage = (currentEarnings / monthlyGoal) * 100;
  const remainingSessions = Math.ceil((monthlyGoal - currentEarnings) / 50); // Assuming $50 per session

  return (
    <div className="min-h-screen bg-sage-50 p-6">
      <div className="mx-auto max-w-4xl">
        <h1 className="mb-8 text-3xl font-bold text-sage-800">Earnings Dashboard</h1>

        <div className="mb-8 grid gap-6 md:grid-cols-2">
          <Card className="p-6">
            <h2 className="mb-4 text-xl font-semibold text-sage-800">Monthly Goal</h2>
            <div className="mb-4">
              <div className="mb-2 flex justify-between">
                <span className="text-sage-600">Progress</span>
                <span className="font-medium text-sage-800">
                  ${currentEarnings} / ${monthlyGoal}
                </span>
              </div>
              <Progress value={progressPercentage} className="h-2" />
            </div>
            <p className="text-sm text-sage-600">
              {remainingSessions} more sessions needed to reach your goal
            </p>
          </Card>

          <Card className="p-6">
            <h2 className="mb-4 text-xl font-semibold text-sage-800">Quick Stats</h2>
            <div className="space-y-4">
              <div>
                <p className="text-sm text-sage-600">This Week's Earnings</p>
                <p className="text-2xl font-bold text-sage-800">$790</p>
              </div>
              <div>
                <p className="text-sm text-sage-600">Average Per Session</p>
                <p className="text-2xl font-bold text-sage-800">$50</p>
              </div>
            </div>
          </Card>
        </div>

        <Card className="p-6">
          <h2 className="mb-6 text-xl font-semibold text-sage-800">Weekly Earnings</h2>
          <div className="h-[300px]">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={earningsData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="week" />
                <YAxis />
                <Tooltip />
                <Line
                  type="monotone"
                  dataKey="earnings"
                  stroke="#4d6d4d"
                  strokeWidth={2}
                />
              </LineChart>
            </ResponsiveContainer>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default TutorEarnings;