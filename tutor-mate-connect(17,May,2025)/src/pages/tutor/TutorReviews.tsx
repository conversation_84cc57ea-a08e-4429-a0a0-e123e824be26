import { Card } from "@/components/ui/card";
import { Star } from "lucide-react";

const reviews = [
  {
    id: 1,
    student: "<PERSON>",
    rating: 5,
    date: "2024-03-01",
    comment: "Excellent tutor! Very patient and explains concepts clearly.",
    subject: "Mathematics",
  },
  {
    id: 2,
    student: "<PERSON>",
    rating: 4,
    date: "2024-02-28",
    comment: "Great teaching style and very knowledgeable.",
    subject: "Physics",
  },
];

const TutorReviews = () => {
  const averageRating =
    reviews.reduce((acc, review) => acc + review.rating, 0) / reviews.length;

  return (
    <div className="min-h-screen bg-sage-50 p-6">
      <div className="mx-auto max-w-4xl">
        <h1 className="mb-8 text-3xl font-bold text-sage-800">My Reviews</h1>

        <Card className="mb-8 p-6">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-semibold text-sage-800">
                Overall Rating
              </h2>
              <div className="mt-2 flex items-center gap-2">
                <span className="text-3xl font-bold text-sage-800">
                  {averageRating.toFixed(1)}
                </span>
                <div className="flex text-yellow-500">
                  {[...Array(5)].map((_, i) => (
                    <Star
                      key={i}
                      className={`h-6 w-6 ${
                        i < Math.round(averageRating)
                          ? "fill-current"
                          : "fill-none"
                      }`}
                    />
                  ))}
                </div>
              </div>
            </div>
            <div className="text-right">
              <p className="text-2xl font-bold text-sage-800">
                {reviews.length}
              </p>
              <p className="text-sage-600">Total Reviews</p>
            </div>
          </div>
        </Card>

        <div className="space-y-4">
          {reviews.map((review) => (
            <Card key={review.id} className="p-6">
              <div className="mb-2 flex items-center justify-between">
                <div>
                  <h3 className="font-semibold text-sage-800">
                    {review.student}
                  </h3>
                  <p className="text-sm text-sage-600">{review.subject}</p>
                </div>
                <span className="text-sm text-sage-500">{review.date}</span>
              </div>
              <div className="mb-2 flex text-yellow-500">
                {[...Array(5)].map((_, i) => (
                  <Star
                    key={i}
                    className={`h-4 w-4 ${
                      i < review.rating ? "fill-current" : "fill-none"
                    }`}
                  />
                ))}
              </div>
              <p className="text-sage-700">{review.comment}</p>
            </Card>
          ))}
        </div>
      </div>
    </div>
  );
};

export default TutorReviews;