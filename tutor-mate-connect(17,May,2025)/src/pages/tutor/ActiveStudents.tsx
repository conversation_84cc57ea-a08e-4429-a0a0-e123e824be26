import { Card } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { User } from "lucide-react";

const students = [
  {
    id: 1,
    name: "<PERSON>",
    subject: "Mathematics",
    sessions: 8,
    nextSession: "2024-03-15",
    progress: 75,
  },
  {
    id: 2,
    name: "<PERSON>",
    subject: "Physics",
    sessions: 5,
    nextSession: "2024-03-16",
    progress: 60,
  },
];

const ActiveStudents = () => {
  return (
    <div className="min-h-screen bg-sage-50 p-6">
      <div className="mx-auto max-w-4xl">
        <h1 className="mb-8 text-3xl font-bold text-sage-800">Active Students</h1>
        
        <div className="space-y-4">
          {students.map((student) => (
            <Card key={student.id} className="p-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <div className="rounded-full bg-sage-100 p-3">
                    <User className="h-6 w-6 text-sage-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-sage-800">{student.name}</h3>
                    <p className="text-sm text-sage-600">{student.subject}</p>
                    <p className="text-sm text-sage-500">
                      Next Session: {student.nextSession}
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-sm font-medium text-sage-700">
                    {student.sessions} Sessions Completed
                  </p>
                  <div className="mt-2 h-2 w-32 rounded-full bg-sage-200">
                    <div
                      className="h-full rounded-full bg-sage-600"
                      style={{ width: `${student.progress}%` }}
                    />
                  </div>
                  <Button
                    variant="outline"
                    className="mt-4"
                  >
                    View Details
                  </Button>
                </div>
              </div>
            </Card>
          ))}
        </div>
      </div>
    </div>
  );
};

export default ActiveStudents;