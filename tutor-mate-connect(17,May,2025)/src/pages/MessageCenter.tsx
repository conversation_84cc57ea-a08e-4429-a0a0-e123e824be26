import { Card } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Search, MessageSquare } from "lucide-react";

const messages = [
  {
    id: 1,
    sender: "<PERSON>",
    subject: "Next Session Confirmation",
    preview: "Hi, I wanted to confirm our next session...",
    date: "2024-03-10",
    unread: true,
  },
  {
    id: 2,
    sender: "<PERSON>",
    subject: "Question about homework",
    preview: "Hello, I have a question about...",
    date: "2024-03-09",
    unread: false,
  },
];

const MessageCenter = () => {
  return (
    <div className="min-h-screen bg-sage-50 p-6">
      <div className="mx-auto max-w-4xl">
        <div className="mb-8 flex items-center justify-between">
          <h1 className="text-3xl font-bold text-sage-800">Message Center</h1>
          <Button className="bg-sage-600 hover:bg-sage-700">
            New Message
          </Button>
        </div>

        <div className="mb-6">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 h-5 w-5 -translate-y-1/2 text-gray-400" />
            <Input
              className="pl-10"
              placeholder="Search messages..."
              type="search"
            />
          </div>
        </div>

        <div className="space-y-4">
          {messages.map((message) => (
            <Card
              key={message.id}
              className={`cursor-pointer p-4 transition-colors hover:bg-sage-50 ${
                message.unread ? "border-l-4 border-sage-600" : ""
              }`}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <MessageSquare className="h-6 w-6 text-sage-600" />
                  <div>
                    <h3 className="font-semibold text-sage-800">
                      {message.sender}
                    </h3>
                    <p className="text-sm font-medium text-sage-700">
                      {message.subject}
                    </p>
                    <p className="text-sm text-sage-600">{message.preview}</p>
                  </div>
                </div>
                <span className="text-sm text-sage-500">{message.date}</span>
              </div>
            </Card>
          ))}
        </div>
      </div>
    </div>
  );
};

export default MessageCenter;