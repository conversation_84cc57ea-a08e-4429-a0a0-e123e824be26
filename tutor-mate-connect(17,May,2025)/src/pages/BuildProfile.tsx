import { useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Progress } from "@/components/ui/progress";
import { Button } from "@/components/ui/button";
import { PersonalInfo } from "@/components/profile/PersonalInfo";
import { ProfessionalInfo } from "@/components/profile/ProfessionalInfo";
import { Availability } from "@/components/profile/Availability";
import { Pricing } from "@/components/profile/Pricing";
import { Verification } from "@/components/profile/Verification";
import { ProfileProvider, useProfile } from "@/contexts/ProfileContext";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { useToast } from "@/components/ui/use-toast";

const steps = [
  { title: "Personal Information", component: PersonalInfo },
  { title: "Professional Details", component: ProfessionalInfo },
  { title: "Availability & Scheduling", component: Availability },
  { title: "Pricing & Payment", component: Pricing },
  { title: "Verification & Documents", component: Verification },
];

const BuildProfileContent = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const { currentStep, setCurrentStep, saveProfile, isLoading } = useProfile();

  const progress = ((currentStep + 1) / steps.length) * 100;

  const handleNext = async () => {
    if (currentStep === steps.length - 1) {
      await saveProfile();
      toast({
        title: "Profile Submitted",
        description: "Your profile has been submitted for review. You'll be notified once it's approved.",
      });
      navigate("/tutor-dashboard");
      return;
    }
    setCurrentStep(currentStep + 1);
  };

  const handleBack = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const CurrentStepComponent = steps[currentStep].component;

  return (
    <div className="min-h-screen bg-background py-8 px-4 sm:px-6 lg:px-8">
      <div className="mx-auto max-w-3xl space-y-8">
        <div className="space-y-2">
          <h2 className="text-2xl font-bold text-center">Build Your Profile</h2>
          <p className="text-muted-foreground text-center">
            Step {currentStep + 1} of {steps.length}: {steps[currentStep].title}
          </p>
        </div>

        <Progress value={progress} className="h-2" />

        <div className="bg-card rounded-lg shadow-lg p-6">
          <CurrentStepComponent />

          <div className="mt-8 flex justify-between">
            <Button
              variant="outline"
              onClick={handleBack}
              disabled={currentStep === 0}
            >
              <ChevronLeft className="mr-2 h-4 w-4" />
              Back
            </Button>
            <Button onClick={handleNext} disabled={isLoading}>
              {currentStep === steps.length - 1 ? (
                isLoading ? "Saving..." : "Complete Profile"
              ) : (
                <>
                  Next
                  <ChevronRight className="ml-2 h-4 w-4" />
                </>
              )}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

const BuildProfile = () => {
  return (
    <ProfileProvider>
      <BuildProfileContent />
    </ProfileProvider>
  );
};

export default BuildProfile;