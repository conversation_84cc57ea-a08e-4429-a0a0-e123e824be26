const About = () => {
  return (
    <div className="min-h-screen bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">About Prime Tutors</h1>
          <p className="text-xl text-gray-600">Connecting Students with Expert Tutors</p>
        </div>
        
        <div className="grid md:grid-cols-2 gap-12 items-center mb-16">
          <div className="space-y-6">
            <h2 className="text-3xl font-bold text-sage-600">Our Mission</h2>
            <p className="text-lg text-gray-600">
              At Prime Tutors, we believe that every student deserves access to quality education
              and personalized support. Our mission is to connect dedicated tutors with eager
              learners, creating an environment where academic excellence thrives.
            </p>
          </div>
          <div className="rounded-lg overflow-hidden shadow-xl">
            <img
              src="https://images.unsplash.com/photo-1649972904349-6e44c42644a7"
              alt="A tutor helping a student with their studies"
              className="w-full h-full object-cover"
            />
          </div>
        </div>

        <div className="grid md:grid-cols-3 gap-8">
          <div className="bg-sage-50 p-6 rounded-lg">
            <h3 className="text-xl font-bold text-sage-600 mb-4">Personalized Learning</h3>
            <p className="text-gray-600">
              We match students with tutors who understand their unique learning style and academic goals.
            </p>
          </div>
          <div className="bg-sage-50 p-6 rounded-lg">
            <h3 className="text-xl font-bold text-sage-600 mb-4">Expert Tutors</h3>
            <p className="text-gray-600">
              Our tutors are carefully selected professionals with proven expertise in their subjects.
            </p>
          </div>
          <div className="bg-sage-50 p-6 rounded-lg">
            <h3 className="text-xl font-bold text-sage-600 mb-4">Flexible Schedule</h3>
            <p className="text-gray-600">
              Book sessions at times that work best for you, with options for both online and in-person tutoring.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default About;