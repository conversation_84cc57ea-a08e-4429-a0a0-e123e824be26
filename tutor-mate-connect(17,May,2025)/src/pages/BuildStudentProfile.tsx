import { useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Progress } from "@/components/ui/progress";
import { Button } from "@/components/ui/button";
import { PersonalInfo } from "@/components/student-profile/PersonalInfo";
import { AcademicDetails } from "@/components/student-profile/AcademicDetails";
import { LearningPreferences } from "@/components/student-profile/LearningPreferences";
import { StudentProfileProvider, useStudentProfile } from "@/contexts/StudentProfileContext";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { useState } from "react";

const steps = [
  { title: "Personal Information", component: PersonalInfo },
  { title: "Academic Details", component: AcademicDetails },
  { title: "Learning Preferences", component: LearningPreferences },
];

const BuildStudentProfileContent = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const { currentStep, setCurrentStep, saveProfile, isLoading, profileData,setProfileData } = useStudentProfile();
  const [showDialog, setShowDialog] = useState(false);
  const [recommendations, setRecommendations] = useState<string[]>([]);

  const progress = ((currentStep + 1) / steps.length) * 100;

  const validateProfile = () => {
    const newRecommendations: string[] = [];
    
    if (!profileData.full_name) newRecommendations.push("Add your full name");
    if (!profileData.email) newRecommendations.push("Add your email address");
    if (!profileData.grade_level) newRecommendations.push("Select your grade level");
    if (!profileData.subjects || profileData.subjects.length === 0) newRecommendations.push("Add subjects you need help with");
    if (!profileData.learning_goals) newRecommendations.push("Share your learning goals");
    
    return newRecommendations;
  };

  const handleNext = async () => {
    if (currentStep === steps.length - 1) {
      const recommendations = validateProfile();
      setRecommendations(recommendations);
      if (recommendations.length > 0) {
        setShowDialog(true);
        return;
      }
      
      await saveProfile();
      toast({
        title: "Profile Submitted",
        description: "Your profile has been created successfully. Welcome aboard!",
      });
      navigate("/student-dashboard");
      return;
    }
    setCurrentStep(currentStep + 1);
  };

  const handleBack = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const CurrentStepComponent = steps[currentStep].component;

  return (
    <div className="min-h-screen bg-background py-8 px-4 sm:px-6 lg:px-8">
      <div className="mx-auto max-w-3xl space-y-8">
        <div className="space-y-2">
          <h2 className="text-2xl font-bold text-center">Build Your Student Profile</h2>
          <p className="text-muted-foreground text-center">
            Step {currentStep + 1} of {steps.length}: {steps[currentStep].title}
          </p>
        </div>

        <Progress value={progress} className="h-2" />

        <div className="bg-card rounded-lg shadow-lg p-6">
          <CurrentStepComponent />

          <div className="mt-8 flex justify-between">
            <Button
              variant="outline"
              onClick={handleBack}
              disabled={currentStep === 0}
            >
              <ChevronLeft className="mr-2 h-4 w-4" />
              Back
            </Button>
            <Button onClick={handleNext} disabled={isLoading}>
              {currentStep === steps.length - 1 ? (
                isLoading ? "Saving..." : "Complete Profile"
              ) : (
                <>
                  Next
                  <ChevronRight className="ml-2 h-4 w-4" />
                </>
              )}
            </Button>
          </div>
        </div>
      </div>

      <Dialog open={showDialog} onOpenChange={setShowDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Profile Review</DialogTitle>
            <DialogDescription>
              We recommend completing the following information to get the most out of your tutoring experience:
            </DialogDescription>
          </DialogHeader>
          <div className="mt-4">
            <ul className="list-disc pl-4 space-y-2">
              {recommendations.map((rec, index) => (
                <li key={index} className="text-muted-foreground">{rec}</li>
              ))}
            </ul>
          </div>
          <div className="flex justify-end space-x-4 mt-6">
            <Button variant="outline" onClick={() => setShowDialog(false)}>
              Go Back
            </Button>
            <Button
              onClick={async () => {
                setShowDialog(false);
                await saveProfile();
                navigate("/student-dashboard");
              }}
            >
              Continue Anyway
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

const BuildStudentProfile = () => {
  return (
    <StudentProfileProvider>
      <BuildStudentProfileContent />
    </StudentProfileProvider>
  );
};

export default BuildStudentProfile;