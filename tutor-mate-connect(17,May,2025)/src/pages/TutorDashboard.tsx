import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/components/ui/use-toast";
import { Card } from "@/components/ui/card";
import { Star } from "lucide-react";

import TutorStats from "@/components/tutor/TutorStats";
import TutorSchedule from "@/components/tutor/TutorSchedule";
import Inbox from "@/components/tutor/Inbox";
import { CalendarSettings } from "@/components/tutor/CalendarSettings";

const TutorDashboard = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const [tutorName, setTutorName] = useState("");
  const [scheduledEvents, setScheduledEvents] = useState([]);
  const [hasCalendlyUsername, setHasCalendlyUsername] = useState(false); 

  useEffect(() => {
    const fetchTutorProfile = async () => {
      try {
        const { data: { user } } = await supabase.auth.getUser();
        if (!user) return;

        const { data, error } = await supabase
          .from('tutor_profiles')
          .select('full_name, calendly_username') 
          .eq('id', user.id)
          .single();

        if (error) throw error;

        if (data) {
          if (data.full_name) {
            setTutorName(data.full_name.split(' ')[0]);
          }
          if (data.calendly_username) {
            setHasCalendlyUsername(true); 
          }
        }
      } catch (error) {
        console.error('Error fetching tutor profile:', error);
        toast({
          title: "Error",
          description: "Failed to load tutor information",
          variant: "destructive",
        });
      }
    };

    fetchTutorProfile();
  }, [toast]);

  return (
    <div className="min-h-screen bg-sage-50 p-6">
      <div className="mx-auto max-w-7xl">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-sage-800">Tutor Dashboard</h1>
          <p className="text-sage-600">
            {tutorName ? `Welcome back ${tutorName}! ` : "Welcome back! "}
            Here's an overview of your teaching activities.
          </p>
        </div>

        <TutorStats />

        
        {!hasCalendlyUsername && (
          <div className="mt-8">
            <CalendarSettings
              setScheduledEvents={setScheduledEvents}
              scheduledEvents={scheduledEvents}
            />
          </div>
        )}

        <div className="mt-8 grid gap-8 lg:grid-cols-2">
          <TutorSchedule  />
          <Inbox />
        </div>

        <div className="mt-8">
          <Card
            className="cursor-pointer p-6 transition-colors hover:bg-sage-50"
            onClick={() => navigate("/tutor/reviews")}
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="rounded-full bg-sage-100 p-3">
                  <Star className="h-6 w-6 text-sage-600" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-sage-800">My Reviews</h3>
                  <p className="text-sm text-sage-600">
                    View all student feedback and ratings
                  </p>
                </div>
              </div>
              <span className="text-sage-600">→</span>
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default TutorDashboard;
