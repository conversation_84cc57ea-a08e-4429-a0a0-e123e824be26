import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";

const Resources = () => {
  return (
    <div className="min-h-screen bg-gradient-to-b from-white to-sage-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">SAT Resources</h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Access our comprehensive collection of SAT preparation materials, study guides, and practice resources designed to help you achieve your target score.
          </p>
        </div>

        {/* Quick Links Section */}
        <div className="grid md:grid-cols-3 gap-6 mb-12">
          <Card className="p-6 hover:shadow-lg transition-shadow">
            <h3 className="text-xl font-bold text-sage-600 mb-4">Practice Tests</h3>
            <ul className="space-y-2 text-gray-600">
              <li className="flex items-center gap-2">
                <Badge variant="secondary">New</Badge>
                Full-Length Practice Tests
              </li>
              <li>Section-Specific Practice</li>
              <li>Timed Mini Tests</li>
              <li>Score Calculator</li>
            </ul>
          </Card>

          <Card className="p-6 hover:shadow-lg transition-shadow">
            <h3 className="text-xl font-bold text-sage-600 mb-4">Study Materials</h3>
            <ul className="space-y-2 text-gray-600">
              <li>Comprehensive Study Guides</li>
              <li>Topic-wise Review Sheets</li>
              <li>Formula Sheets</li>
              <li>Vocabulary Lists</li>
            </ul>
          </Card>

          <Card className="p-6 hover:shadow-lg transition-shadow">
            <h3 className="text-xl font-bold text-sage-600 mb-4">Test Strategies</h3>
            <ul className="space-y-2 text-gray-600">
              <li>Time Management Tips</li>
              <li>Question Analysis</li>
              <li>Common Pitfalls</li>
              <li>Test Day Preparation</li>
            </ul>
          </Card>
        </div>

        {/* Detailed Sections */}
        <div className="space-y-8">
          <Accordion type="single" collapsible className="w-full">
            <AccordionItem value="math">
              <AccordionTrigger className="text-lg font-semibold">
                SAT Math Resources
              </AccordionTrigger>
              <AccordionContent className="space-y-4 p-4">
                <div className="grid md:grid-cols-2 gap-4">
                  <div>
                    <h4 className="font-semibold mb-2">Topics Covered:</h4>
                    <ul className="list-disc list-inside space-y-1 text-gray-600">
                      <li>Algebra and Functions</li>
                      <li>Problem Solving and Data Analysis</li>
                      <li>Advanced Math and Geometry</li>
                      <li>Trigonometry and Complex Numbers</li>
                    </ul>
                  </div>
                  <div>
                    <h4 className="font-semibold mb-2">Available Resources:</h4>
                    <ul className="list-disc list-inside space-y-1 text-gray-600">
                      <li>Topic-wise Practice Problems</li>
                      <li>Step-by-step Solutions</li>
                      <li>Formula Sheets and Quick References</li>
                      <li>Video Tutorials</li>
                    </ul>
                  </div>
                </div>
              </AccordionContent>
            </AccordionItem>

            <AccordionItem value="reading">
              <AccordionTrigger className="text-lg font-semibold">
                SAT Reading Resources
              </AccordionTrigger>
              <AccordionContent className="space-y-4 p-4">
                <div className="grid md:grid-cols-2 gap-4">
                  <div>
                    <h4 className="font-semibold mb-2">Reading Strategies:</h4>
                    <ul className="list-disc list-inside space-y-1 text-gray-600">
                      <li>Active Reading Techniques</li>
                      <li>Main Idea Identification</li>
                      <li>Evidence-Based Questions</li>
                      <li>Time Management Tips</li>
                    </ul>
                  </div>
                  <div>
                    <h4 className="font-semibold mb-2">Practice Materials:</h4>
                    <ul className="list-disc list-inside space-y-1 text-gray-600">
                      <li>Passage Analysis Exercises</li>
                      <li>Vocabulary Building Tools</li>
                      <li>Reading Comprehension Tests</li>
                      <li>Literary Device Practice</li>
                    </ul>
                  </div>
                </div>
              </AccordionContent>
            </AccordionItem>

            <AccordionItem value="writing">
              <AccordionTrigger className="text-lg font-semibold">
                SAT Writing Resources
              </AccordionTrigger>
              <AccordionContent className="space-y-4 p-4">
                <div className="grid md:grid-cols-2 gap-4">
                  <div>
                    <h4 className="font-semibold mb-2">Writing Skills:</h4>
                    <ul className="list-disc list-inside space-y-1 text-gray-600">
                      <li>Grammar and Punctuation Rules</li>
                      <li>Sentence Structure</li>
                      <li>Rhetorical Skills</li>
                      <li>Essay Writing Techniques</li>
                    </ul>
                  </div>
                  <div>
                    <h4 className="font-semibold mb-2">Study Materials:</h4>
                    <ul className="list-disc list-inside space-y-1 text-gray-600">
                      <li>Grammar Practice Tests</li>
                      <li>Essay Templates and Examples</li>
                      <li>Writing Style Guides</li>
                      <li>Common Error Corrections</li>
                    </ul>
                  </div>
                </div>
              </AccordionContent>
            </AccordionItem>
          </Accordion>
        </div>

        {/* Additional Resources */}
        <div className="mt-12 bg-white rounded-lg shadow-lg p-6">
          <h3 className="text-xl font-bold text-sage-600 mb-4">Additional Resources</h3>
          <div className="grid md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-semibold mb-2">Study Planning</h4>
              <ul className="space-y-2 text-gray-600">
                <li>• Customized Study Schedules</li>
                <li>• Progress Tracking Tools</li>
                <li>• Goal Setting Worksheets</li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-2">Support Services</h4>
              <ul className="space-y-2 text-gray-600">
                <li>• One-on-One Tutoring</li>
                <li>• Study Groups</li>
                <li>• Practice Test Reviews</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Resources;