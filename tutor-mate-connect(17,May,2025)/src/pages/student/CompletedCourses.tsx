import { Card } from "@/components/ui/card";
import { CheckCircle } from "lucide-react";

const courses = [
  {
    id: 1,
    subject: "Basic Mathematics",
    tutor: "<PERSON>",
    completedDate: "2024-02-15",
    sessionsCompleted: 12,
    grade: "A",
  },
  {
    id: 2,
    subject: "Introduction to Chemistry",
    tutor: "<PERSON>",
    completedDate: "2024-01-30",
    sessionsCompleted: 10,
    grade: "A-",
  },
];

const CompletedCourses = () => {
  return (
    <div className="min-h-screen bg-sage-50 p-6">
      <div className="mx-auto max-w-4xl">
        <h1 className="mb-8 text-3xl font-bold text-sage-800">
          Completed Courses
        </h1>

        <div className="space-y-6">
          {courses.map((course) => (
            <Card key={course.id} className="p-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <div className="rounded-full bg-sage-100 p-3">
                    <CheckCircle className="h-6 w-6 text-sage-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-sage-800">
                      {course.subject}
                    </h3>
                    <p className="text-sm text-sage-600">
                      Tutor: {course.tutor}
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <span className="rounded-full bg-sage-100 px-3 py-1 text-sm font-medium text-sage-700">
                    Grade: {course.grade}
                  </span>
                </div>
              </div>

              <div className="mt-4 flex items-center justify-between text-sm text-sage-600">
                <span>Completed: {course.completedDate}</span>
                <span>{course.sessionsCompleted} Sessions Completed</span>
              </div>

              <hr className="my-4 border-sage-200" />

              <div className="flex justify-between">
                <button className="text-sm font-medium text-sage-600 hover:text-sage-700">
                  View Certificate →
                </button>
                <button className="text-sm font-medium text-sage-600 hover:text-sage-700">
                  Course Details →
                </button>
              </div>
            </Card>
          ))}
        </div>
      </div>
    </div>
  );
};

export default CompletedCourses;