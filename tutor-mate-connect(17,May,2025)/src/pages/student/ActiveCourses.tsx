import { Card } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { BookOpen, Calendar } from "lucide-react";

const courses = [
  {
    id: 1,
    subject: "Advanced Mathematics",
    tutor: "<PERSON>",
    progress: 75,
    nextSession: "2024-03-15",
    completedSessions: 6,
    totalSessions: 8,
  },
  {
    id: 2,
    subject: "Physics Fundamentals",
    tutor: "<PERSON>",
    progress: 40,
    nextSession: "2024-03-16",
    completedSessions: 4,
    totalSessions: 10,
  },
];

const ActiveCourses = () => {
  return (
    <div className="min-h-screen bg-sage-50 p-6">
      <div className="mx-auto max-w-4xl">
        <h1 className="mb-8 text-3xl font-bold text-sage-800">Active Courses</h1>

        <div className="space-y-6">
          {courses.map((course) => (
            <Card key={course.id} className="p-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <div className="rounded-full bg-sage-100 p-3">
                    <BookOpen className="h-6 w-6 text-sage-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-sage-800">
                      {course.subject}
                    </h3>
                    <p className="text-sm text-sage-600">
                      Tutor: {course.tutor}
                    </p>
                  </div>
                </div>
                <Button variant="outline" className="gap-2">
                  <Calendar className="h-4 w-4" />
                  Schedule Session
                </Button>
              </div>

              <div className="mt-4">
                <div className="mb-2 flex justify-between text-sm">
                  <span className="text-sage-600">Course Progress</span>
                  <span className="font-medium text-sage-700">
                    {course.completedSessions} of {course.totalSessions} Sessions
                  </span>
                </div>
                <div className="h-2 rounded-full bg-sage-200">
                  <div
                    className="h-full rounded-full bg-sage-600"
                    style={{ width: `${course.progress}%` }}
                  />
                </div>
              </div>

              <div className="mt-4 flex items-center justify-between text-sm">
                <span className="text-sage-600">
                  Next Session: {course.nextSession}
                </span>
                <Button variant="link" className="text-sage-600 hover:text-sage-700">
                  View Details →
                </Button>
              </div>
            </Card>
          ))}
        </div>
      </div>
    </div>
  );
};

export default ActiveCourses;