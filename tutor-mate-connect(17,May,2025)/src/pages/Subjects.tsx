import { useState } from "react";
import { Book, Code, Calculator, TestTube, Music, Globe } from "lucide-react";
import { SubjectCard } from "@/components/SubjectCard";
import { TestimonialsSection } from "@/components/sections/TestimonialsSection";

const subjects = [
  {
    name: "Mathematics",
    description: "Algebra, Calculus, Statistics, and more",
    icon: Calculator,
    tutorCount: 45,
  },
  {
    name: "Sciences",
    description: "Physics, Chemistry, Biology",
    icon: TestTube,
    tutorCount: 38,
  },
  {
    name: "Programming",
    description: "Python, Java, Web Development",
    icon: Code,
    tutorCount: 29,
  },
  {
    name: "Languages",
    description: "English, Spanish, Mandarin",
    icon: Globe,
    tutorCount: 52,
  },
  {
    name: "Music",
    description: "Piano, Guitar, Music Theory",
    icon: Music,
    tutorCount: 25,
  },
  {
    name: "Literature",
    description: "Essay Writing, Reading Comprehension",
    icon: Book,
    tutorCount: 33,
  },
];

const HowItWorks = () => {
  return (
    <div className="bg-sage-50 py-24">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div className="text-center">
          <h2 className="text-3xl font-bold text-sage-800 sm:text-4xl">
            How Prime Tutors Works
          </h2>
          <p className="mt-4 text-lg text-sage-600">
            Get started with expert tutoring in three simple steps
          </p>
        </div>
        <div className="mt-16 grid gap-8 md:grid-cols-3">
          {[
            {
              step: "1",
              title: "Choose Your Subject",
              description:
                "Browse our wide range of subjects and select the one you need help with.",
            },
            {
              step: "2",
              title: "Find Your Perfect Tutor",
              description:
                "Review tutor profiles, ratings, and experience to find your ideal match.",
            },
            {
              step: "3",
              title: "Start Learning",
              description:
                "Schedule your first session and begin your journey to academic success.",
            },
          ].map((item) => (
            <div
              key={item.step}
              className="relative rounded-lg bg-white p-6 shadow-sm"
            >
              <div className="mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-sage-100 text-sage-600">
                <span className="text-xl font-bold">{item.step}</span>
              </div>
              <h3 className="mb-2 text-xl font-semibold text-sage-800">
                {item.title}
              </h3>
              <p className="text-sage-600">{item.description}</p>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

const Subjects = () => {
  return (
    <div className="min-h-screen bg-white">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-12">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">Our Subjects</h1>
          <p className="text-xl text-gray-600">
            Explore our wide range of subjects taught by expert tutors
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {subjects.map((subject) => (
            <SubjectCard
              key={subject.name}
              subject={subject.name}
              tutorCount={subject.tutorCount}
              description={subject.description}
            />
          ))}
        </div>
      </div>

      <HowItWorks />
      
      <TestimonialsSection />
    </div>
  );
};

export default Subjects;