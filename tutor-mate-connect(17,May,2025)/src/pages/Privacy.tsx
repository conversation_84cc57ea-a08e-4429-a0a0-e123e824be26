import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";

const Privacy = () => {
  return (
    <div className="container mx-auto px-4 py-8">
      <Card>
        <CardHeader>
          <CardTitle className="text-2xl font-bold">Privacy Policy</CardTitle>
        </CardHeader>
        <CardContent className="prose max-w-none">
          <h2>1. Information We Collect</h2>
          <p>We collect information you provide directly to us when using our platform.</p>

          <h2>2. How We Use Your Information</h2>
          <ul>
            <li>To provide and maintain our services</li>
            <li>To communicate with you</li>
            <li>To improve our platform</li>
          </ul>

          <h2>3. Information Sharing</h2>
          <p>We do not sell your personal information to third parties.</p>

          <h2>4. Data Security</h2>
          <p>We implement appropriate security measures to protect your personal information.</p>

          <h2>5. Your Rights</h2>
          <p>You have the right to access, correct, or delete your personal information.</p>
        </CardContent>
      </Card>
    </div>
  );
};

export default Privacy;