import { Button } from "@/components/ui/button";
import { useNavigate } from "react-router-dom";
import { TestimonialCard } from "@/components/TestimonialCard";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { CheckCircle2, GraduationCap, Clock, CreditCard } from "lucide-react";

const BecomeTutor = () => {
  const navigate = useNavigate();

  const benefits = [
    {
      icon: <CreditCard className="h-6 w-6 text-sage-600" />,
      title: "Earn More",
      description: "We take only a small commission so you keep more of what you earn.",
    },
    {
      icon: <GraduationCap className="h-6 w-6 text-sage-600" />,
      title: "Find Students Easily",
      description: "We match you with students looking for your expertise.",
    },
    {
      icon: <Clock className="h-6 w-6 text-sage-600" />,
      title: "Simplify Scheduling",
      description: "Set your availability and let students book directly with you.",
    },
    {
      icon: <CreditCard className="h-6 w-6 text-sage-600" />,
      title: "Get Paid Seamlessly",
      description: "Receive payments quickly and securely through our platform.",
    },
  ];

  const testimonials = [
    {
      content: "Prime Tutors has helped me find consistent, well-paying tutoring gigs!",
      author: "Sarah Johnson",
      rating: 5,
      imageUrl: "/tutors/tutor-1.jpg",
    },
    {
      content: "The scheduling tools are a game-changer. I can focus on teaching without the admin hassle.",
      author: "Michael Chen",
      rating: 5,
      imageUrl: "/tutors/tutor-2.jpg",
    },
  ];

  const faqs = [
    {
      question: "How much can I earn as a tutor?",
      answer: "Your earnings depend on your expertise, subjects, and hours. Most tutors earn between $25-75 per hour, with specialized subjects commanding higher rates.",
    },
    {
      question: "What qualifications do I need?",
      answer: "We require tutors to have a degree in their subject area or relevant teaching experience. Each application is reviewed individually.",
    },
    {
      question: "How do I get paid?",
      answer: "Payments are processed securely through our platform after each session. You can withdraw your earnings to your bank account at any time.",
    },
    {
      question: "How do I set my schedule?",
      answer: "You have complete control over your availability. Use our calendar tool to set your preferred teaching hours.",
    },
    {
      question: "What subjects can I teach?",
      answer: "You can teach any subject you're qualified in. We support academic subjects, test prep, languages, and professional skills.",
    },
  ];

  return (
    <div className="flex flex-col min-h-screen">
      {/* Hero Section */}
      <section className="relative bg-sage-50 px-6 py-24 sm:px-8">
        <div className="absolute inset-0 z-0">
          <div className="absolute inset-0 bg-[radial-gradient(circle_500px_at_50%_200px,#e6ede6,transparent)]" />
        </div>
        <div className="relative z-10 mx-auto max-w-4xl text-center">
          <h1 className="animate-fade-up text-4xl font-bold text-sage-900 sm:text-5xl md:text-6xl">
            Earn More. Teach More. Stress Less.
          </h1>
          <p className="animate-fade-up mt-6 text-lg text-sage-700 [animation-delay:200ms] sm:text-xl">
            Connect with students, earn more than ever, and enjoy an easy-to-use platform tailored for educators like you.
          </p>
          <div className="mt-8">
            <Button
              size="lg"
              className="bg-sage-600 hover:bg-sage-700"
              onClick={() => navigate("/tutor-signup")}
            >
              Sign Up Now
            </Button>
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section className="px-6 py-24 bg-white sm:px-8">
        <div className="mx-auto max-w-7xl">
          <h2 className="text-3xl font-bold text-center text-sage-900 sm:text-4xl">
            How It Works
          </h2>
          <p className="mt-4 text-center text-lg text-sage-600">
            3 Simple Steps to Get Started
          </p>

          <div className="mt-16 grid gap-8 md:grid-cols-3">
            {[
              {
                title: "Create Your Profile",
                description: "Tell us about your expertise, subjects you teach, and your availability.",
                icon: <GraduationCap className="h-12 w-12 text-sage-600" />,
                action: "Sign Up Now",
              },
              {
                title: "Get Verified",
                description: "Our team will review your qualifications to ensure students find the best tutors.",
                icon: <CheckCircle2 className="h-12 w-12 text-sage-600" />,
                action: "Learn About Our Vetting Process",
              },
              {
                title: "Start Teaching and Earning",
                description: "Once approved, students can book sessions with you, and you'll get paid after each session.",
                icon: <CreditCard className="h-12 w-12 text-sage-600" />,
                action: "Join Today",
              },
            ].map((step, index) => (
              <div
                key={index}
                className="flex flex-col items-center text-center p-6 rounded-lg bg-sage-50"
              >
                <div className="mb-4">{step.icon}</div>
                <h3 className="text-xl font-semibold text-sage-900 mb-2">{step.title}</h3>
                <p className="text-sage-600 mb-4">{step.description}</p>
                <Button
                  variant="outline"
                  className="border-sage-600 text-sage-600 hover:bg-sage-50"
                  onClick={() => navigate("/tutor-signup")}
                >
                  {step.action}
                </Button>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="px-6 py-24 bg-sage-50 sm:px-8">
        <div className="mx-auto max-w-7xl">
          <h2 className="text-3xl font-bold text-center text-sage-900 sm:text-4xl">
            Join Prime Tutors
          </h2>
          <div className="mt-16 grid gap-8 md:grid-cols-2 lg:grid-cols-4">
            {benefits.map((benefit, index) => (
              <div
                key={index}
                className="flex flex-col items-center text-center p-6 rounded-lg bg-white"
              >
                <div className="mb-4">{benefit.icon}</div>
                <h3 className="text-xl font-semibold text-sage-900 mb-2">{benefit.title}</h3>
                <p className="text-sage-600">{benefit.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="px-6 py-24 bg-white sm:px-8">
        <div className="mx-auto max-w-7xl">
          <h2 className="text-3xl font-bold text-center text-sage-900 sm:text-4xl mb-16">
            What Our Tutors Say
          </h2>
          <div className="grid gap-8 md:grid-cols-2">
            {testimonials.map((testimonial, index) => (
              <TestimonialCard key={index} {...testimonial} />
            ))}
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="px-6 py-24 bg-sage-50 sm:px-8">
        <div className="mx-auto max-w-3xl">
          <h2 className="text-3xl font-bold text-center text-sage-900 sm:text-4xl mb-16">
            Frequently Asked Questions
          </h2>
          <Accordion type="single" collapsible className="w-full">
            {faqs.map((faq, index) => (
              <AccordionItem key={index} value={`item-${index}`}>
                <AccordionTrigger className="text-left">
                  {faq.question}
                </AccordionTrigger>
                <AccordionContent>{faq.answer}</AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
        </div>
      </section>

      {/* Final CTA Section */}
      <section className="px-6 py-24 bg-white sm:px-8">
        <div className="mx-auto max-w-4xl text-center">
          <h2 className="text-3xl font-bold text-sage-900 sm:text-4xl">
            Ready to start earning more while teaching what you love?
          </h2>
          <div className="mt-8">
            <Button
              size="lg"
              className="bg-sage-600 hover:bg-sage-700"
              onClick={() => navigate("/tutor-signup")}
            >
              Sign Up as a Tutor Now
            </Button>
          </div>
        </div>
      </section>
    </div>
  );
};

export default BecomeTutor;