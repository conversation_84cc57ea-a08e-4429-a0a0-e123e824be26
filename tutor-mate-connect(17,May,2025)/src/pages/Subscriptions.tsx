import { useEffect, useState } from "react";
import { supabase } from "@/integrations/supabase/client";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useToast } from "@/components/ui/use-toast"; 

const SUPABASE_FUNCTIONS_URL = `${import.meta.env.VITE_SUPABASE_URL}/functions/v1`;

type Plan = {
  id: number;
  name: string;
  price: number;
  sessions_count: number;
  description: string;
};

export default function Subscriptions() {
  const [plans, setPlans] = useState<Plan[]>([]);
  const [loading, setLoading] = useState(true);
  const [processingPlanId, setProcessingPlanId] = useState<number | null>(null);
  const [studentId, setStudentId] = useState<string | null>(null);
  const [studentEmail, setStudentEmail] = useState<string | null>(null);
  const toast = useToast().toast; 

  useEffect(() => {
    
    const handlePaymentRedirect = () => {
      const params = new URLSearchParams(window.location.search);
      const paymentStatus = params.get('payment');
      
      if (paymentStatus === 'success') {
        
        window.history.replaceState({}, document.title, window.location.pathname);
        
        
        if (toast) {
          toast({
            title: "Payment Successful",
            description: "Your subscription has been processed successfully.",
            variant: "default",
          });
        } else {
          alert("Payment successful! Your subscription has been processed.");
        }
      } else if (paymentStatus === 'cancel') {
        
        window.history.replaceState({}, document.title, window.location.pathname);
        
        if (toast) {
          toast({
            title: "Payment Cancelled",
            description: "Your payment was cancelled. Please try again.",
            variant: "destructive",
          });
        } else {
          alert("Payment cancelled. Please try again if you wish to subscribe.");
        }
      }
    };

    
    const fetchPlans = async () => {
      const { data, error } = await supabase
        .from("subscription_plan")
        .select("id, name, price, sessions_count, description")
        .order("price", { ascending: true });

      if (error) {
        console.error("Error fetching plans:", error.message);
      } else {
        setPlans(data || []);
      }

      setLoading(false);
    };

    const getUser = async () => {
      const {
        data: { user },
      } = await supabase.auth.getUser();
    
      if (user?.id) {
        setStudentId(user.id);
    
        const { data: profile, error } = await supabase
          .from("student_profiles")
          .select("email")
          .eq("id", user.id)
          .single();
    
        if (error) {
          console.error("Error fetching student email:", error.message);
        } else {
          setStudentEmail(profile?.email || user.email || null);
        }
      }
    };
    
    handlePaymentRedirect();
    fetchPlans();
    getUser();
  }, [toast]);


  const handleSelectPlan = async (planId: number) => {
    const selectedPlan = plans.find((p) => p.id === planId);
  
    if (!studentId || !selectedPlan) {
      if (toast) {
        toast({
          title: "Error",
          description: "Missing required data to proceed. Please make sure you're logged in.",
          variant: "destructive",
        });
      } else {
        alert("Missing required data to proceed. Please make sure you're logged in.");
      }
      return;
    }

    
    localStorage.setItem("subscriptionFlow", "subscriptionPage");
    
    setProcessingPlanId(planId);
  
    try {
      
      const { data: userData } = await supabase.auth.getUser();
      const userEmail = studentEmail || userData?.user?.email;

      if (!userEmail) {
        throw new Error("Could not retrieve user email");
      }

      const response = await fetch(
        `${SUPABASE_FUNCTIONS_URL}/create-stripe-subscription`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${import.meta.env.VITE_SUPABASE_ANON_KEY}`,
          },
          body: JSON.stringify({
            studentEmail: userEmail,
            studentId,
            planId: selectedPlan.id,
            planName: selectedPlan.name,
            planPriceId: selectedPlan.price,
            planSessions: selectedPlan.sessions_count,
          }),
        }
      );
      
      const result = await response.json();
      console.log("Response from stripe endpoint:", result);
      
      if (response.ok && result.url) {
        
        window.location.href = result.url;
      } else {
        throw new Error(result.error || "Failed to create checkout session");
      }
    } catch (err: any) {
      console.error("Error creating subscription:", err);
      if (toast) {
        toast({
          title: "Error",
          description: `Failed to process payment: ${err.message}`,
          variant: "destructive",
        });
      } else {
        alert(`Something went wrong: ${err.message}`);
      }
    } finally {
      setProcessingPlanId(null);
    }
  };

  return (
    <div className="max-w-7xl mx-auto px-6 py-12 space-y-16">
      <section className="text-center space-y-6">
      <h1 className="text-3xl font-bold text-sage-800">
          Choose Your Perfect Plan
        </h1>
        <p className="text-xl text-sage-600 max-w-2xl mx-auto">
          Unlock access to personalized sessions and start learning today. Select
          the plan that suits your needs.
        </p>
      </section>

      <section>
        <h2 className="text-3xl font-bold text-sage-800 text-center">
          Available Plans
        </h2>

        {loading ? (
          <p className="text-center mt-6">Loading plans...</p>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-8">
            {plans.map((plan, idx) => (
              <Card
  key={plan.id}
  className={`rounded-3xl p-6 shadow-lg transition-all duration-300 transform hover:scale-105 ${
    idx === 1
      ? "border-4 border-green-700 bg-green-50"
      : "border border-green-200"
  } hover:border-green-800 hover:bg-green-100`}
>

                <CardContent className="flex flex-col items-center text-center space-y-4">
                  {idx === 1 && (
                    <div className="bg-sage-800 text-white text-xs font-semibold px-4 py-2 rounded-full shadow-lg">
                      Best Value
                    </div>
                  )}
                  <h3 className="text-3xl font-semibold text-sage-800">
                    {plan.name}
                  </h3>
                  <p className="text-4xl font-extrabold text-sage-800">
                    ${plan.price}
                  </p>
                    <p className="text-sage-600">
                    {plan.sessions_count} Sessions Included
                  </p>
                  <p className="text-sm text-sage-600">{plan.description}</p>
                  <Button
  onClick={() => handleSelectPlan(plan.id)}
  disabled={processingPlanId !== null}
  className={`mt-6 px-8 py-3 text-lg rounded-full ${
    idx === 1
      ? "bg-sage-800 hover:bg-green-800 text-white"
      : "bg-sage-800 hover:bg-green-800 text-white"
  } ${processingPlanId === plan.id ? "opacity-50 cursor-not-allowed" : ""}`}
>
  {processingPlanId === plan.id ? "Processing..." : `Select ${plan.name}`}
</Button>

                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </section>
    </div>
  );
}