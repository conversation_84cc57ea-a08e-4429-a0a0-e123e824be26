import { Button } from "@/components/ui/button";
import { useNavigate } from "react-router-dom";
import { PopularSubjects } from "@/components/sections/PopularSubjects";
import { TestimonialsSection } from "@/components/sections/TestimonialsSection";
import { useToast } from "@/components/ui/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { useEffect, useState } from "react";
import { TutorCard } from "@/components/TutorCard";

interface FeaturedTutor {
  id: string;
  full_name: string;
  profile_photo_url: string;
  bio: string;
  hourly_rate: number;
  subjects: string[];
  rating: number;
}

const Index = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const [user, setUser] = useState(null);
  const [featuredTutors, setFeaturedTutors] = useState<FeaturedTutor[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const checkUser = async () => {
      const { data: { session } } = await supabase.auth.getSession();
      setUser(session?.user ?? null);
    };

    const fetchFeaturedTutors = async () => {
      try {
        setLoading(true);
        const { data: tutors, error } = await supabase
          .from('tutor_profiles')
          .select(`
            id,
            full_name,
            profile_photo_url,
            bio,
            hourly_rate,
            tutor_subjects (
              supported_subjects (
                name,
                display_name
              )
            ),
            tutor_metrics (
              rating
            )
          `)
          .eq('is_verified', true)
          .limit(3);

        if (error) {
          console.error('Error fetching tutors:', error);
          return;
        }

        const formattedTutors: FeaturedTutor[] = tutors.map(tutor => ({
          id: tutor.id,
          full_name: tutor.full_name || 'Anonymous Tutor',
          profile_photo_url: tutor.profile_photo_url || '/placeholder.svg',
          bio: tutor.bio || 'No bio available',
          hourly_rate: tutor.hourly_rate || 0,
          rating: Number(tutor.tutor_metrics?.[0]?.rating || 0),
          subjects: tutor.tutor_subjects
            ? tutor.tutor_subjects
                .filter(ts => ts.supported_subjects) // Filter out null subjects
                .map(ts => ts.supported_subjects.display_name)
                .filter(Boolean) // Filter out any undefined/null values
            : []
        }));

        setFeaturedTutors(formattedTutors);
      } catch (error) {
        console.error('Error fetching tutors:', error);
      } finally {
        setLoading(false);
      }
    };

    checkUser();
    fetchFeaturedTutors();
  }, []);

  const handleFindTutor = async () => {
    if (!user) {
      toast({
        title: "Authentication Required",
        description: "Please create an account or sign in to browse available tutors.",
        duration: 5000,
      });
      navigate("/student-signup");
      return;
    }
    navigate("/find-tutor");
  };

  return (
    <div className="flex min-h-screen flex-col">
      {/* Hero Section */}
      <section className="relative flex min-h-[600px] items-center justify-center bg-sage-50 px-6 py-24 sm:px-8">
        <div className="absolute inset-0 z-0">
          <div className="absolute inset-0 bg-[radial-gradient(circle_500px_at_50%_200px,#e6ede6,transparent)]" />
          <div className="absolute inset-0 bg-gradient-to-r from-sage-600/20 to-sage-800/20" />
        </div>
        <div className="z-10 mx-auto max-w-4xl text-center">
          <h1 className="animate-fade-up text-4xl font-bold text-sage-900 sm:text-5xl md:text-6xl">
            Learn From The Best Tutors
          </h1>
          <p className="animate-fade-up mt-6 text-lg text-sage-700 [animation-delay:200ms] sm:text-xl">
            Connect with expert tutors, learn at your own pace, and achieve your
            academic goals with personalized one-on-one tutoring.
          </p>
          <div className="animate-fade-up mt-8 flex flex-col items-center gap-4 sm:flex-row sm:justify-center [animation-delay:400ms]">
            <Button
              size="lg"
              onClick={handleFindTutor}
              className="w-full bg-sage-600 hover:bg-sage-700 sm:w-auto"
            >
              Find a Tutor
            </Button>
            <Button
              variant="outline"
              size="lg"
              onClick={() => navigate("/become-tutor")}
              className="w-full border-sage-600 text-sage-600 hover:bg-sage-50 sm:w-auto"
            >
              Become a Tutor
            </Button>
          </div>
        </div>
      </section>

      {/* Featured Tutors */}
      <section className="px-6 py-24 sm:px-8">
        <div className="mx-auto max-w-7xl">
          <div className="text-center">
            <h2 className="text-3xl font-bold text-sage-800 sm:text-4xl">
              Featured Tutors
            </h2>
            <p className="mt-4 text-lg text-sage-600">
              Learn from our highly qualified and experienced tutors
            </p>
          </div>
          <div className="mt-16 grid gap-8 sm:grid-cols-2 lg:grid-cols-3">
            {loading ? (
              <p className="col-span-full text-center text-sage-600">Loading tutors...</p>
            ) : featuredTutors.length > 0 ? (
              featuredTutors.map((tutor) => (
                <TutorCard
                  key={tutor.id}
                  name={tutor.full_name}
                  rating={tutor.rating}
                  hourlyRate={tutor.hourly_rate}
                  imageUrl={tutor.profile_photo_url}
                  specialties={tutor.subjects}
                  bio={tutor.bio}
                  onBookSession={handleFindTutor}
                />
              ))
            ) : (
              <p className="col-span-full text-center text-sage-600">No featured tutors available at the moment.</p>
            )}
          </div>
        </div>
      </section>

      {/* Popular Subjects */}
      <PopularSubjects />

      {/* Testimonials */}
      <TestimonialsSection />
    </div>
  );
};

export default Index;