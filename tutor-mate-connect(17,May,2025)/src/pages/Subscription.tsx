import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";

const SubscriptionTerms = () => {
  return (
    <div className="container mx-auto px-4 py-8">
      <Card>
        <CardHeader>
          <CardTitle className="text-2xl font-bold">Prime Tutors Subscription Terms</CardTitle>
        </CardHeader>
        <CardContent className="prose max-w-none">
          <h2>1. Subscription Plans</h2>
          <p>Prime Tutors offers various subscription plans for both students and tutors.</p>

          <h2>2. Billing</h2>
          <p>Subscriptions are billed automatically according to your chosen billing cycle.</p>

          <h2>3. Cancellation</h2>
          <p>You can cancel your subscription at any time. Refunds are handled according to our refund policy.</p>

          <h2>4. Changes to Subscription</h2>
          <p>Prime Tutors reserves the right to modify subscription terms with appropriate notice.</p>

          <h2>5. Payment Processing</h2>
          <p>All payments are processed securely through our payment providers.</p>
        </CardContent>
      </Card>
    </div>
  );
};

export default SubscriptionTerms;