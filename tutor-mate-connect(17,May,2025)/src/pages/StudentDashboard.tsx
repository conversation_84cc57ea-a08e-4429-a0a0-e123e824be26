import StudentStats from "@/components/student/StudentStats";
import UpcomingSessions from "@/components/student/UpcomingSessions";
import BookSession from "@/components/student/BookSession";
import { useEffect, useState } from "react";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";

const StudentDashboard = () => {
  const { toast } = useToast();
  const [studentName, setStudentName] = useState("");
  const [refreshUpcoming, setRefreshUpcoming] = useState(0); 
  const [upcomingCount, setUpcomingCount] = useState(0);

  useEffect(() => {
    const fetchStudentName = async () => {
      try {
        const { data: { user }, error: authError } = await supabase.auth.getUser();
        if (authError || !user) throw new Error("Authentication error: User not found.");
        const userId = user.id;

        const { data: studentData } = await supabase
          .from("student_profiles")
          .select("full_name")
          .eq("id", userId)
          .maybeSingle();

        if (studentData?.full_name) {
          setStudentName(studentData.full_name.split(" ")[0]);
          return;
        }

        const { data: profileData } = await supabase
          .from("profiles")
          .select("full_name")
          .eq("id", userId)
          .maybeSingle();

        if (profileData?.full_name) {
          setStudentName(profileData.full_name.split(" ")[0]);
        }
      } catch (error: any) {
        console.error("Error fetching student name:", error.message);
        toast({
          title: "Error",
          description: "Failed to load student information",
          variant: "destructive",
        });
      }
    };

    fetchStudentName();
  }, [toast]);

  const handleBookingComplete = () => {
    setRefreshUpcoming((prev) => prev + 1);
  };

  return (
    <div className="min-h-screen bg-sage-50 p-6">
      <div className="mx-auto max-w-7xl">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-sage-800">Student Dashboard</h1>
          <p className="text-sage-600">
            {studentName ? `Welcome back ${studentName}! ` : "Welcome back! "}
            Here's an overview of your learning journey.
          </p>
        </div>

        <StudentStats upcomingCount={upcomingCount}/>

        <div className="grid gap-8 lg:grid-cols-2">
          <UpcomingSessions refreshTrigger={refreshUpcoming} onUpcomingCountChange={(count) => setUpcomingCount(count)}/>
          <BookSession onBookingComplete={handleBookingComplete} />
        </div>
      </div>
    </div>
  );
};

export default StudentDashboard;

