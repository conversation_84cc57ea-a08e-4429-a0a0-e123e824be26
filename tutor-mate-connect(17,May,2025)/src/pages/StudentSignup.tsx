import { useState, useEffect } from "react";
import { useNavi<PERSON>, Link } from "react-router-dom";
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useToast } from "@/hooks/use-toast";
import { Mail, Eye, EyeOff } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";

const baseUrl =  import.meta.env.VITE_BASE_URL;
const StudentSignup = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    password: "",
  });
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);

  const currentPath = window.location.pathname;
  useEffect(() => {
    localStorage.setItem("signupRoute", currentPath);
  },[])

  
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    const { name, email, password } = formData;

    if (!name || !email || !password) {
      toast({
        title: "Error",
        description: "Please fill in all fields",
        variant: "destructive",
      });
      return;
    }

    setLoading(true);
    const { error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          full_name: name,
          role: "student",
        },
      },
    });

    if (error) {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    } else {
      toast({
        title: "Sign Up Successful",
        description: "Please complete your profile to get started.",
      });
      navigate("/build-student-profile");
    }
    setLoading(false);
  };
  const handleGoogleSignup = async () => {
    
    localStorage.setItem("userRole", "student");
    const { error } = await supabase.auth.signInWithOAuth({
      provider: 'google',
      options: {
        redirectTo: `${baseUrl}/auth/v1/callback`,
      },
    });

    if (error) {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    }
  };
  

  return (
    <div className="flex min-h-screen items-center justify-center bg-background p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="space-y-2">
          <CardTitle className="text-2xl font-bold">Sign up as a student</CardTitle>
          <p className="text-sm text-muted-foreground">
            Already have an account?{" "}
            <Link to="/login" className="text-sage-600 hover:underline">
              Log in
            </Link>
          </p>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Button variant="outline" className="w-full" onClick={handleGoogleSignup} disabled={loading}>
              <Mail className="mr-2 h-4 w-4" />
              Continue with Google
            </Button>

            <div className="relative flex justify-center text-xs uppercase">
              <span className="bg-background px-2 text-muted-foreground">or</span>
              <div className="absolute inset-x-0 top-1/2 h-px -translate-y-1/2 bg-muted"></div>
            </div>

            <form onSubmit={handleSubmit} className="space-y-4">
              <div>
                <Input
                  name="name"
                  placeholder="Your name"
                  value={formData.name}
                  onChange={handleChange}
                  disabled={loading}
                />
              </div>
              <div>
                <Input
                  name="email"
                  type="email"
                  placeholder="Your email"
                  value={formData.email}
                  onChange={handleChange}
                  disabled={loading}
                />
              </div>
              <div className="relative">
                <Input
                  name="password"
                  type={showPassword ? "text" : "password"}
                  placeholder="Your password"
                  value={formData.password}
                  onChange={handleChange}
                  disabled={loading}
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-3 top-1/2 -translate-y-1/2"
                  disabled={loading}
                >
                  {showPassword ? <EyeOff className="h-4 w-4 text-gray-500" /> : <Eye className="h-4 w-4 text-gray-500" />}
                </button>
              </div>

              <Button type="submit" className="w-full bg-sage-600 hover:bg-sage-700" disabled={loading}>
                {loading ? "Signing up..." : "Sign up"}
              </Button>
            </form>

            <p className="text-center text-xs text-muted-foreground">
              By clicking Continue or Sign up, you agree to{" "}
              <Link to="/terms" className="text-sage-600 hover:underline">
                Prime Tutors Terms of Use
              </Link>
              , including{" "}
              <Link to="/subscription-terms" className="text-sage-600 hover:underline">
                Subscription Terms
              </Link>{" "}
              and{" "}
              <Link to="/privacy" className="text-sage-600 hover:underline">
                Privacy Policy
              </Link>
              .
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default StudentSignup;
