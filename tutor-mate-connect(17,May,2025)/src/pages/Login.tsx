import { useState, useEffect } from "react";
import { useN<PERSON><PERSON>, Link, useLocation } from "react-router-dom";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { LoginForm } from "@/components/auth/LoginForm";
import { SocialLogin } from "@/components/auth/SocialLogin";


const Login = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const baseUrl =  import.meta.env.VITE_BASE_URL;
  useEffect(() => {
    const checkSession = async () => {
      const { data: { session } } = await supabase.auth.getSession();
      if (session?.user) {
        await setUserRoleAndRedirect(session.user.email);
      }
    };

    checkSession();

    const { data: { subscription } } = supabase.auth.onAuthStateChange((_event, session) => {
      if (session?.user) {
        setUserRoleAndRedirect(session.user.email);
      }
    });

    return () => subscription.unsubscribe();
  }, [location.pathname]);

  
  const setUserRoleAndRedirect = async (email: string) => {
    try {
      let userRole = "";

      
      const { data: profileData, error: profileError } = await supabase
        .from('profiles')
        .select('role')
        .eq('email', email)
        .maybeSingle();

      if (profileError) throw profileError;

      if (profileData?.role) {
        userRole = profileData.role;
        localStorage.setItem("userRole", userRole); 
      }

      
      const { data: tutorData, error: tutorError } = await supabase
        .from('tutor_profiles')
        .select('email')
        .eq('email', email)
        .maybeSingle();

      if (tutorError) throw tutorError;

      if (tutorData) {
        userRole = "tutor"; 
        localStorage.setItem("userRole", userRole); 
      }

      
      const { data: studentData, error: studentError } = await supabase
        .from('student_profiles')
        .select('email')
        .eq('email', email)
        .maybeSingle();

      if (studentError) throw studentError;

      if (studentData) {
        userRole = "student"; 
        localStorage.setItem("userRole", userRole);
      }

      
      if (userRole) {
        
        navigate(userRole === "student" ? "/student-dashboard" : "/tutor-dashboard", { replace: true });
      } else {
        console.warn("No matching user role found for email:", email);
      }
    } catch (error) {
      console.error("Error fetching user role:", error);
    }
  };

  const handleLogin = async (email: string, password: string) => {
    if (!email || !password) {
      toast({
        title: "Error",
        description: "Please fill in all fields",
        variant: "destructive",
      });
      return;
    }

    setLoading(true);
    try {
      const { data: { user }, error } = await supabase.auth.signInWithPassword({ email, password });

      if (error) throw error;

      if (user) {
        toast({
          title: "Login Successful",
          description: "Welcome back!",
        });
        await setUserRoleAndRedirect(user.email);
      }
    } catch (error) {
      console.error('Login error:', error);
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleGoogleLogin = async () => {
    const { error } = await supabase.auth.signInWithOAuth({
      provider: 'google',
      options: {
        redirectTo: `${baseUrl}auth/v1/callback`,
      },
    });
    if (error) {
      toast({
        title: "Error",
        description: error.message, 
        variant: "destructive",
      });
      return;
    }
    const { data: { user } } = await supabase.auth.getUser();
    if (user) {
      await setUserRoleAndRedirect(user.email);
    }
  };

  return (
    <div className="flex min-h-screen items-center justify-center bg-background p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="space-y-2">
          <CardTitle className="text-2xl font-bold">Log in</CardTitle>
          <p className="text-sm text-muted-foreground">
            <Link to="/student-signup" className="text-sage-600 hover:underline">
              Sign up as a student
            </Link>{" "}
            or{" "}
            <Link to="/tutor-signup" className="text-sage-600 hover:underline">
              Sign up as a tutor
            </Link>
          </p>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <SocialLogin onGoogleLogin={handleGoogleLogin} loading={loading} />
            <div className="relative flex justify-center text-xs uppercase">
              <span className="bg-background px-2 text-muted-foreground">or</span>
              <div className="absolute inset-x-0 top-1/2 h-px -translate-y-1/2 bg-muted"></div>
            </div>
            <LoginForm onSubmit={handleLogin} loading={loading} />
            <p className="text-center text-xs text-muted-foreground">
              By clicking Log in or Continue with, you agree to{" "}
              <Link to="/terms" className="text-sage-600 hover:underline">
                Prime Tutors Terms of Use
              </Link>{" "}
              and{" "}
              <Link to="/privacy" className="text-sage-600 hover:underline">
                Privacy Policy
              </Link>
              .
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default Login;


