import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Card } from "@/components/ui/card";
import { TutorCard } from "@/components/TutorCard";
import { TutorSearch } from "@/components/search/TutorSearch";
import { useToast } from "@/components/ui/use-toast";
import { supabase } from "@/integrations/supabase/client";

const FindTutor = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const [tutors, setTutors] = useState([]);
  const [loading, setLoading] = useState(true);
  const [filters, setFilters] = useState({
    category: "all",
    hourlyRate: [0, 200],
    language: "all",
  });

  useEffect(() => {
    fetchTutors();
  }, [filters]);

  const fetchTutors = async () => {
    try {
      let query = supabase
        .from('tutor_profiles')
        .select(`
          *,
          tutor_languages!inner (
            supported_languages!inner(name)
          ),
          tutor_subjects!inner (
            supported_subjects!inner(category)
          )
        `)
        .order('created_at', { ascending: false });

      
      if (filters.category !== 'all') {
        query = query.eq('tutor_subjects.supported_subjects.category', filters.category);
      }
      
      if (filters.language !== 'all') {
        query = query.eq('tutor_languages.supported_languages.name', filters.language);
      }
      
      if (filters.hourlyRate[1] < 200) {
        query = query.lte('hourly_rate', filters.hourlyRate[1])
          .gte('hourly_rate', filters.hourlyRate[0]);
      }

      const { data, error } = await query;

      if (error) throw error;
      setTutors(data || []);
    } catch (error) {
      console.error('Error fetching tutors:', error);
      toast({
        title: "Error",
        description: "Failed to load tutors. Please try again later.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleFilterChange = (newFilters) => {
    setFilters(newFilters);
  };

  const handleBookSession = async (tutorId: string) => {
    const { data: { session } } = await supabase.auth.getSession();
    
    if (!session) {
      toast({
        title: "Authentication Required",
        description: "Please sign in or create an account to book a session",
        variant: "default",
      });
      navigate("/login");
      return;
    }

    navigate(`/book-session/${tutorId}`);
  };

  return (
    <div className="min-h-screen bg-sage-50 p-6">
      <div className="mx-auto max-w-7xl">
        <h1 className="mb-8 text-3xl font-bold text-sage-800">Find a Tutor</h1>
        
        <div className="grid gap-8 lg:grid-cols-4">
          <div className="lg:col-span-1">
            <TutorSearch onFilterChange={handleFilterChange} />
          </div>

          <div className="lg:col-span-3">
            {loading ? (
              <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                {[1, 2, 3].map((i) => (
                  <Card key={i} className="h-96 animate-pulse bg-white" />
                ))}
              </div>
            ) : (
              <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                {tutors.map((tutor) => (
                  <TutorCard
                    key={tutor.id}
                    name={tutor.full_name}
                    subject={tutor.subjects?.[0] || "General"}
                    rating={4.5}
                    hourlyRate={tutor.hourly_rate || 0}
                    imageUrl={tutor.profile_photo_url || "/placeholder.svg"}
                    specialties={tutor.subjects || []}
                    onBookSession={() => handleBookSession(tutor.id)}
                  />
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default FindTutor;