import React, { createContext, useContext, useState } from "react";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";

type StudentProfileContextType = {
  currentStep: number;
  setCurrentStep: (step: number) => void;
  profileData: StudentProfileData;
  setProfileData: (data: StudentProfileData) => void;
  saveProfile: () => Promise<void>;
  isLoading: boolean;
};

type StudentProfileData = {
  full_name: string;
  profile_photo_url: string; 
  email: string;
  phone_number: string;
  location: string;
  timezone: string;
  grade_level: string;
  subjects: string[];
  learning_goals: string;
  learning_style: string;
  preferred_languages: string[];
  special_needs: string;
  parent_info: {
    name: string;
    email: string;
    phone: string;
  };
};

const StudentProfileContext = createContext<StudentProfileContextType | undefined>(undefined);

export const StudentProfileProvider = ({ children }: { children: React.ReactNode }) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();
  const [profileData, setProfileData] = useState<StudentProfileData>({
    full_name: "",
    profile_photo_url: "",
    email: "",
    phone_number: "",
    location: "",
    timezone: "",
    grade_level: "",
    subjects: [],
    learning_goals: "",
    learning_style: "",
    preferred_languages: [],
    special_needs: "",
    parent_info: {
      name: "",
      email: "",
      phone: "",
    },});

  const saveProfile = async () => {
    setIsLoading(true);
    try {
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) throw new Error("No user found");

      const { error } = await supabase
        .from('student_profiles')
        .upsert({
          id: user.id,
          full_name: profileData.full_name,
          email: user.email,
          phone_number: profileData.phone_number,
          location: profileData.location,
          timezone: profileData.timezone,
          profile_photo_url: profileData.profile_photo_url,
          grade_level: profileData.grade_level,
          subjects: profileData.subjects,
          learning_goals: profileData.learning_goals,
          learning_style: profileData.learning_style,
          preferred_languages: profileData.preferred_languages,
          special_needs: profileData.special_needs,
          parent_info: profileData.parent_info,
          updated_at: new Date().toISOString(),
        });

      if (error) throw error;

      toast({
        title: "Profile saved",
        description: "Your profile has been successfully saved.",
      });
    } catch (error) {
      console.error('Error saving profile:', error);
      toast({
        title: "Error",
        description: "Failed to save profile. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <StudentProfileContext.Provider
      value={{
        currentStep,
        setCurrentStep,
        profileData,
        setProfileData,
        saveProfile,
        isLoading,
      }}
    >
      {children}
    </StudentProfileContext.Provider>
  );
};

export const useStudentProfile = () => {
  const context = useContext(StudentProfileContext);
  if (context === undefined) {
    throw new Error("useStudentProfile must be used within a StudentProfileProvider");
  }
  return context;
};
