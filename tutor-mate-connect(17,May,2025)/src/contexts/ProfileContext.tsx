import React, { createContext, useContext, useState } from "react";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/components/ui/use-toast";

type ProfileContextType = {
  currentStep: number;
  setCurrentStep: (step: number) => void;
  profileData: any;
  setProfileData: (data: any) => void;
  saveProfile: () => Promise<void>;
  isLoading: boolean;
  uploadFile: (file: File, folder: string) => Promise<string | null>;
};

const ProfileContext = createContext<ProfileContextType | undefined>(undefined);

export const ProfileProvider = ({ children }: { children: React.ReactNode }) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();
  const [profileData, setProfileData] = useState({
    fullName: "",
    profilePhoto: "",
    email: "",
    phone: "",
    location: "",
    timezone: "",
    bio: "",
    qualifications: [],
    experience: "",
    specializations: [],
    languages: [],
    teachingStyle: "",
    availability: [],
    sessionDuration: "",
    maxStudents: "1", 
    flexibleScheduling: false,
    hourlyRate: "",
    sessionPackages: [],
    paymentMethod: "",
    verification_documents: {
      background_check: null,
      certifications: null,
      id_verification: null,
    },
  });

  const uploadFile = async (file: File, folder: string): Promise<string | null> => {
    try {
      const fileName = `${folder}/${Date.now()}_${file.name}`;
      const { error } = await supabase.storage.from("tutor_files").upload(fileName, file);
      if (error) throw error;

      
      const { data: { publicUrl } } = supabase.storage.from("tutor_files").getPublicUrl(fileName);

      return publicUrl;
    } catch (error) {
      console.error("Error uploading file:", error);
      toast({
        title: "Upload Error",
        description: "Failed to upload the file. Please try again.",
        variant: "destructive",
      });
      return null;
    }
  };

  const saveProfile = async () => {
    setIsLoading(true);
    try {
      const { data: { user } } = await supabase.auth.getUser();

      if (!user) throw new Error("No user found");

      
      const transformedData = {
        id: user.id,
        full_name: profileData.fullName,
        profile_photo_url: profileData.profilePhoto,
        email: profileData.email,
        phone_number: profileData.phone,
        location: profileData.location,
        timezone: profileData.timezone,
        bio: profileData.bio,
        qualifications: profileData.qualifications,
        teaching_experience: profileData.experience,
        teaching_style: profileData.teachingStyle,
        availability: profileData.availability,
        session_duration: [profileData.sessionDuration],
        max_students_per_session: parseInt(profileData.maxStudents) || 1,
        flexible_scheduling: profileData.flexibleScheduling,
        hourly_rate: parseFloat(profileData.hourlyRate) || 0,
        session_packages: profileData.sessionPackages,
        payment_method: profileData.paymentMethod,
        verification_documents: profileData.verification_documents,
        profile_status: "pending_review",
        profile_completion_percentage: 100,
        updated_at: new Date().toISOString(),
      };

      
      const { error: tutorProfileError } = await supabase
        .from("tutor_profiles")
        .upsert(transformedData);

      if (tutorProfileError) throw tutorProfileError;

      toast({
        title: "Profile saved",
        description: "Your profile has been successfully saved and is pending review.",
      });
    } catch (error) {
      console.error("Error saving profile:", error);
      toast({
        title: "Error",
        description: "Failed to save profile. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <ProfileContext.Provider
      value={{
        currentStep,
        setCurrentStep,
        profileData,
        setProfileData,
        saveProfile,
        isLoading,
        uploadFile,
      }}
    >
      {children}
    </ProfileContext.Provider>
  );
};

export const useProfile = () => {
  const context = useContext(ProfileContext);
  if (context === undefined) {
    throw new Error("useProfile must be used within a ProfileProvider");
  }
  return context;
};
