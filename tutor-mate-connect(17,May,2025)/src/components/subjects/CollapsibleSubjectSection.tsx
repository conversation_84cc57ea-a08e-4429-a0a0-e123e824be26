import { useState } from "react";
import { SubjectCard } from "@/components/SubjectCard";
import { TutorSearchFilters } from "@/components/search/TutorSearchFilters";
import { TutorSearchResults } from "@/components/search/TutorSearchResults";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { cn } from "@/lib/utils";

interface Subject {
  name: string;
  description: string;
  icon: React.ElementType;
  tutorCount: number;
}

interface CollapsibleSubjectSectionProps {
  subject: Subject;
  isSelected: boolean;
  onSelect: (name: string) => void;
}

export const CollapsibleSubjectSection = ({
  subject,
  isSelected,
  onSelect,
}: CollapsibleSubjectSectionProps) => {
  return (
    <Collapsible
      open={isSelected}
      onOpenChange={() => onSelect(subject.name)}
      className={cn(
        "transition-all duration-300",
        isSelected ? "col-span-full" : "col-span-1"
      )}
    >
      <CollapsibleTrigger className="w-full">
        <SubjectCard
          subject={subject.name}
          tutorCount={subject.tutorCount}
          description={subject.description}
          className={cn(
            "transition-all duration-300",
            isSelected && "border-sage-500"
          )}
        />
      </CollapsibleTrigger>
      
      <CollapsibleContent className="mt-6">
        <div className="grid gap-8 lg:grid-cols-4">
          <div className="lg:col-span-1">
            <TutorSearchFilters onFilterChange={() => {}} />
          </div>
          <div className="lg:col-span-3">
            <TutorSearchResults
              tutors={[]}
              loading={false}
              onLoadMore={() => {}}
              hasMore={false}
            />
          </div>
        </div>
      </CollapsibleContent>
    </Collapsible>
  );
};