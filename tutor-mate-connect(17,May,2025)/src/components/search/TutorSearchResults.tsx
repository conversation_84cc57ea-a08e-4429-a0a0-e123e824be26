import { useState } from "react";
import { TutorCard } from "@/components/TutorCard";
import { Button } from "@/components/ui/button";

interface Tutor {
  id: string;
  name: string;
  subject: string;
  rating: number;
  hourlyRate: number;
  imageUrl: string;
  specialties: string[];
}

interface TutorSearchResultsProps {
  tutors: <PERSON><PERSON>[];
  loading: boolean;
  onLoadMore: () => void;
  hasMore: boolean;
}

export const TutorSearchResults = ({
  tutors,
  loading,
  onLoadMore,
  hasMore,
}: TutorSearchResultsProps) => {
  return (
    <div className="space-y-6">
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {tutors.map((tutor) => (
          <TutorCard
            key={tutor.id}
            name={tutor.name}
            subject={tutor.subject}
            rating={tutor.rating}
            hourlyRate={tutor.hourlyRate}
            imageUrl={tutor.imageUrl}
            specialties={tutor.specialties}
          />
        ))}
      </div>
      {hasMore && (
        <div className="flex justify-center">
          <Button
            onClick={onLoadMore}
            variant="outline"
            disabled={loading}
            className="min-w-[200px]"
          >
            {loading ? "Loading..." : "Load More"}
          </Button>
        </div>
      )}
    </div>
  );
};