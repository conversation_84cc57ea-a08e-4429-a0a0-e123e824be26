import { useState, useEffect } from "react";
import { <PERSON>lide<PERSON> } from "@/components/ui/slider";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { fetchSubjects, type Subject } from "@/data/subjects";
import { useToast } from "@/components/ui/use-toast";

interface TutorSearchFiltersProps {
  onFilterChange: (filters: any) => void;
}

export const TutorSearchFilters = ({ onFilterChange }: TutorSearchFiltersProps) => {
  const [subjects, setSubjects] = useState<Subject[]>([]);
  const [loading, setLoading] = useState(true);
  const { toast } = useToast();
  const [filters, setFilters] = useState({
    subject: "all",
    hourlyRate: [0, 200],
    teachingStyle: "any",
    language: "any",
    minRating: 0,
    isVerified: false,
  });

  useEffect(() => {
    const loadSubjects = async () => {
      try {
        const data = await fetchSubjects();
        
        setSubjects(data);
      } catch (error) {
        console.error('Error loading subjects:', error);
        toast({
          title: "Error",
          description: "Failed to load subject categories. Please try again.",
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };

    loadSubjects();
  }, [toast]);

  const handleFilterChange = (key: string, value: any) => {
    const newFilters = { ...filters, [key]: value };
    setFilters(newFilters);
    onFilterChange(newFilters);
  };

  
  const categories = Array.from(new Set(subjects.map(subject => subject.category)))
    .filter(Boolean) 
    .sort();

  

  return (
    <div className="space-y-6 p-4 bg-white rounded-lg shadow">
      <div className="space-y-2">
        <Label>Subject Category</Label>
        <Select
          value={filters.subject}
          onValueChange={(value) => handleFilterChange("subject", value)}
          disabled={loading}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select category" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Categories</SelectItem>
            {categories.map((category) => (
              <SelectItem key={category} value={category}>
                {category}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-2">
        <Label>Hourly Rate Range ($)</Label>
        <Slider
          defaultValue={[0, 200]}
          max={200}
          step={5}
          onValueChange={(value) => handleFilterChange("hourlyRate", value)}
        />
        <div className="flex justify-between text-sm text-muted-foreground">
          <span>${filters.hourlyRate[0]}</span>
          <span>${filters.hourlyRate[1]}</span>
        </div>
      </div>

      <div className="space-y-2">
        <Label>Teaching Style</Label>
        <Select
          value={filters.teachingStyle}
          onValueChange={(value) => handleFilterChange("teachingStyle", value)}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select style" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="any">Any Style</SelectItem>
            <SelectItem value="interactive">Interactive</SelectItem>
            <SelectItem value="structured">Structured</SelectItem>
            <SelectItem value="flexible">Flexible</SelectItem>
            <SelectItem value="practical">Practical</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-2">
        <Label>Language</Label>
        <Select
          value={filters.language}
          onValueChange={(value) => handleFilterChange("language", value)}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select language" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="any">Any Language</SelectItem>
            <SelectItem value="english">English</SelectItem>
            <SelectItem value="spanish">Spanish</SelectItem>
            <SelectItem value="french">French</SelectItem>
            <SelectItem value="mandarin">Mandarin</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-2">
        <Label>Minimum Rating</Label>
        <Select
          value={filters.minRating.toString()}
          onValueChange={(value) => handleFilterChange("minRating", Number(value))}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select minimum rating" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="0">Any Rating</SelectItem>
            <SelectItem value="3">3+ Stars</SelectItem>
            <SelectItem value="4">4+ Stars</SelectItem>
            <SelectItem value="4.5">4.5+ Stars</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="flex items-center justify-between">
        <Label>Verified Tutors Only</Label>
        <Switch
          checked={filters.isVerified}
          onCheckedChange={(checked) => handleFilterChange("isVerified", checked)}
        />
      </div>
    </div>
  );
};