import { useState, useEffect } from "react";
import { Label } from "@/components/ui/label";
import { Slider } from "@/components/ui/slider";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { fetchSubjects } from "@/data/subjects";
import { fetchLanguages } from "@/data/languages";
import { useToast } from "@/components/ui/use-toast";

interface SearchFilters {
  category: string;
  hourlyRate: [number, number];
  language: string;
}

interface TutorSearchProps {
  onFilterChange: (filters: SearchFilters) => void;
}

export const TutorSearch = ({ onFilterChange }: TutorSearchProps) => {
  const [categories, setCategories] = useState<string[]>([]);
  const [languages, setLanguages] = useState<string[]>([]);
  const [loading, setLoading] = useState(true);
  const { toast } = useToast();
  const [filters, setFilters] = useState<SearchFilters>({
    category: "all",
    hourlyRate: [0, 200],
    language: "all",
  });

  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);
        
        
        const subjectsData = await fetchSubjects();
        
        const uniqueCategories = Array.from(
          new Set(subjectsData.map((subject) => subject.category))
        ).filter(Boolean).sort();
        
        setCategories(uniqueCategories);

        
        const languagesData = await fetchLanguages();
        
        const languageNames = languagesData.map(lang => lang.name).sort();
        
        setLanguages(languageNames);
      } catch (error) {
        console.error('Error loading data:', error);
        toast({
          title: "Error",
          description: "Failed to load search options. Please try again.",
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [toast]);

  const handleFilterChange = (key: keyof SearchFilters, value: any) => {
    const newFilters = { ...filters, [key]: value };
    setFilters(newFilters);
    onFilterChange(newFilters);
  };

  return (
    <div className="space-y-6 p-6 bg-white rounded-lg shadow-sm">
      <div className="space-y-2">
        <Label>Subject Category</Label>
        <Select
          value={filters.category}
          onValueChange={(value) => handleFilterChange("category", value)}
          disabled={loading}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select category" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Categories</SelectItem>
            {categories.map((category) => (
              <SelectItem key={category} value={category}>
                {category}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-2">
        <Label>Hourly Rate Range ($)</Label>
        <Slider
          defaultValue={[0, 200]}
          max={200}
          step={5}
          onValueChange={(value) => handleFilterChange("hourlyRate", value)}
        />
        <div className="flex justify-between text-sm text-muted-foreground">
          <span>${filters.hourlyRate[0]}</span>
          <span>${filters.hourlyRate[1]}</span>
        </div>
      </div>

      <div className="space-y-2">
        <Label>Language</Label>
        <Select
          value={filters.language}
          onValueChange={(value) => handleFilterChange("language", value)}
          disabled={loading}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select language" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">Any Language</SelectItem>
            {languages.map((language) => (
              <SelectItem key={language} value={language}>
                {language}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
    </div>
  );
};