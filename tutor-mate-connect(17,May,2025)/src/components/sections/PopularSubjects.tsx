import { Button } from "@/components/ui/button";
import { Book } from "lucide-react";
import { SubjectCard } from "@/components/SubjectCard";
import { useNavigate } from "react-router-dom";

const staticSubjects = [
  {
    id: 1,
    display_name: "Mathematics",
    category: "STEM",
    tutor_count: 45,
  },
  {
    id: 2,
    display_name: "Physics",
    category: "Sciences",
    tutor_count: 38,
  },
  {
    id: 3,
    display_name: "Computer Science",
    category: "Technology",
    tutor_count: 32,
  },
  {
    id: 4,
    display_name: "English Literature",
    category: "Languages",
    tutor_count: 41,
  },
];

export const PopularSubjects = () => {
  const navigate = useNavigate();

  return (
    <section className="bg-sage-50 px-6 py-24 sm:px-8">
      <div className="mx-auto max-w-7xl">
        <div className="text-center">
          <h2 className="text-3xl font-bold text-sage-800 sm:text-4xl">
            Popular Subjects
          </h2>
          <p className="mt-4 text-lg text-sage-600">
            Explore our wide range of subjects taught by expert tutors
          </p>
        </div>
        <div className="mt-16 grid gap-8 sm:grid-cols-2 lg:grid-cols-4">
          {staticSubjects.map((subject) => (
            <SubjectCard
              key={subject.id}
              subject={subject.display_name}
              tutorCount={subject.tutor_count}
              description={`${subject.category} - ${subject.display_name}`}
              topics={[]}
            />
          ))}
        </div>
        <div className="mt-12 text-center">
          <Button
            variant="outline"
            size="lg"
            onClick={() => {
              navigate("/subjects", { replace: true });
              window.scrollTo(0, 0);
            }}
            className="border-sage-600 text-sage-600 hover:bg-sage-100"
          >
            <Book className="mr-2 h-4 w-4" />
            View All Subjects
          </Button>
        </div>
      </div>
    </section>
  );
};