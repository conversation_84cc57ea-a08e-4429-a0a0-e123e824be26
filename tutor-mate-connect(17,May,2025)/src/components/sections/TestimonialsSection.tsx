import { TestimonialCard } from "@/components/TestimonialCard";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";
import { useEffect, useState } from "react";
import { cn } from "@/lib/utils";

const testimonials = [
  {
    author: "<PERSON>",
    role: "Student",
    content:
      "The tutoring platform has been a game-changer for my studies. The personalized attention helped me improve my grades significantly.",
    imageUrl: "https://images.unsplash.com/photo-1494790108377-be9c29b29330?w=400&h=400&auto=format&fit=crop&crop=face",
    rating: 5,
  },
  {
    author: "<PERSON>",
    role: "Tutor",
    content:
      "Being able to connect with students and help them achieve their goals has been incredibly rewarding. The platform makes it easy to manage my schedule.",
    imageUrl: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=400&auto=format&fit=crop&crop=face",
    rating: 5,
  },
  {
    author: "<PERSON>",
    role: "Parent",
    content:
      "Finding qualified tutors for my children has never been easier. The results speak for themselves - both their confidence and grades have improved.",
    imageUrl: "https://images.unsplash.com/photo-1489424731084-a5d8b219a5bb?w=400&h=400&auto=format&fit=crop&crop=face",
    rating: 5,
  },
  {
    author: "David Thompson",
    role: "Student",
    content:
      "I was struggling with calculus until I found this platform. My tutor broke down complex concepts into simple, understandable pieces. Now I'm acing my tests!",
    imageUrl: "https://images.unsplash.com/photo-1463453091185-61582044d556?w=400&h=400&auto=format&fit=crop&crop=face",
    rating: 4,
  },
  {
    author: "Lisa Wang",
    role: "Parent",
    content:
      "As a working parent, I appreciate how flexible the scheduling is. My daughter's grades in science have improved dramatically since starting with her tutor.",
    imageUrl: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=400&h=400&auto=format&fit=crop&crop=face",
    rating: 5,
  },
  {
    author: "James Wilson",
    role: "Student",
    content:
      "The quality of tutors on this platform is exceptional. They're not just knowledgeable but also great at explaining complex topics in an engaging way.",
    imageUrl: "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=400&h=400&auto=format&fit=crop&crop=face",
    rating: 5,
  },
  {
    author: "Maria Garcia",
    role: "Tutor",
    content:
      "I love how the platform connects me with students who are truly eager to learn. The tools provided make online tutoring seamless and effective.",
    imageUrl: "https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=400&h=400&auto=format&fit=crop&crop=face",
    rating: 5,
  },
  {
    author: "Alex Kim",
    role: "Student",
    content:
      "The instant messaging feature makes it easy to get quick help when I'm stuck on a problem. My tutor is always responsive and helpful.",
    imageUrl: "https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?w=400&h=400&auto=format&fit=crop&crop=face",
    rating: 4,
  },
];

export const TestimonialsSection = () => {
  const [api, setApi] = useState<any>();
  const [current, setCurrent] = useState(0);

  useEffect(() => {
    if (!api) {
      return;
    }

    api.on("select", () => {
      setCurrent(api.selectedScrollSnap());
    });
  }, [api]);

  return (
    <section className="px-6 py-24 sm:px-8">
      <div className="mx-auto max-w-7xl">
        <div className="text-center">
          <h2 className="text-3xl font-bold text-sage-800 sm:text-4xl">
            What Our Users Say
          </h2>
          <p className="mt-4 text-lg text-sage-600">
            Read about the experiences of our students and tutors
          </p>
        </div>
        <div className="mt-16">
          <Carousel
            opts={{
              align: "start",
              loop: true,
            }}
            className="w-full"
            setApi={setApi}
          >
            <CarouselContent className="-ml-2 md:-ml-4">
              {testimonials.map((testimonial, index) => (
                <CarouselItem
                  key={testimonial.author}
                  className="pl-2 md:pl-4 md:basis-1/2 lg:basis-1/3"
                >
                  <div className="h-full">
                    <TestimonialCard {...testimonial} delay={index * 100 + 200} />
                  </div>
                </CarouselItem>
              ))}
            </CarouselContent>
            <div className="hidden md:block">
              <CarouselPrevious className="absolute -left-12 top-1/2" />
              <CarouselNext className="absolute -right-12 top-1/2" />
            </div>
            <div className="mt-8 flex justify-center gap-2">
              {testimonials.map((_, index) => (
                <button
                  key={index}
                  className={cn(
                    "h-2 w-2 rounded-full transition-all",
                    current === index ? "bg-sage-600 w-4" : "bg-sage-200"
                  )}
                  onClick={() => api?.scrollTo(index)}
                  aria-label={`Go to slide ${index + 1}`}
                />
              ))}
            </div>
          </Carousel>
        </div>
      </div>
    </section>
  );
};
