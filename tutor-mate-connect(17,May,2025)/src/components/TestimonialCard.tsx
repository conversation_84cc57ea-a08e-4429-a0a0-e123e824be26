import { Card } from "@/components/ui/card";
import { Star } from "lucide-react";

interface TestimonialCardProps {
  content: string;
  author: string;
  rating: number;
  imageUrl: string;
  delay?: number;
}

export const TestimonialCard = ({
  content,
  author,
  rating,
  imageUrl,
}: TestimonialCardProps) => {
  return (
    <Card className="overflow-hidden">
      <div className="p-6">
        <div className="mb-4 flex gap-1">
          {Array.from({ length: 5 }).map((_, i) => (
            <Star
              key={i}
              className={`h-4 w-4 ${
                i < rating ? "fill-yellow-400 text-yellow-400" : "text-gray-200"
              }`}
            />
          ))}
        </div>
        <p className="mb-6 text-lg text-muted-foreground">{content}</p>
        <div className="flex items-center gap-4">
          <img
            src={imageUrl}
            alt={author}
            className="h-12 w-12 rounded-full object-cover"
            loading="lazy"
          />
          <div>
            <p className="font-semibold">{author}</p>
            <p className="text-sm text-muted-foreground">Student</p>
          </div>
        </div>
      </div>
    </Card>
  );
};