
import { useState, useEffect, useRef } from "react";
import { Card } from "@/components/ui/card";
import { useToast } from "@/components/ui/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { useSearchParams } from "react-router-dom";
import { InlineWidget } from "react-calendly";
import { Button } from "@/components/ui/button";
import { ChevronLeft, ChevronRight } from "lucide-react";

const SUPABASE_FUNCTIONS_URL = `${import.meta.env.VITE_SUPABASE_URL}/functions/v1`;

const BookSession = ({onBookingComplete}) => {
  const [selectedTutor, setSelectedTutor] = useState(null);
  const [tutors, setTutors] = useState([]);
  const [filteredTutors, setFilteredTutors] = useState([]);
  const [tutorEventTypes, setTutorEventTypes] = useState([]);
  const [availableTimes, setAvailableTimes] = useState([]);
  const [eventName, setEventName] = useState(null);
  const [studentId, setStudentId] = useState(null);
  const [searchParams] = useSearchParams();
  const [calendlyUrl, setCalendlyUrl] = useState(null);
  const [lastBookedEventUri, setLastBookedEventUri] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [loadingEvents, setLoadingEvents] = useState(false);
  const [loadingSlots, setLoadingSlots] = useState(false);
  const [loadingTutors, setLoadingTutors] = useState(false);
  const [subjects, setSubjects] = useState([]);
  const [selectedSubject, setSelectedSubject] = useState(null);
  const [bookingStep, setBookingStep] = useState("subjects");
  const [subscription, setSubscription] = useState(null); 
  const [loadingSubscription, setLoadingSubscription] = useState(true);
  const [selectedPricingModel, setSelectedPricingModel] = useState(null);
  const [selectedSubscriptionPlan, setSelectedSubscriptionPlan] = useState(null);
  const [subscriptionPlans, setSubscriptionPlans] = useState([]);
  const [pendingSubscriptionUse, setPendingSubscriptionUse] = useState(false);


  const { toast } = useToast();

  const slotsPerPage = 10;
  const totalPages = Math.ceil(availableTimes.length / slotsPerPage);
  const indexOfLastSlot = currentPage * slotsPerPage;
  const indexOfFirstSlot = indexOfLastSlot - slotsPerPage;
  const currentSlots = availableTimes.slice(indexOfFirstSlot, indexOfLastSlot);

const [studentEmail, setStudentEmail] = useState(null);
const [studentName, setStudentName] = useState(null);


useEffect(() => {
  const fetchStudentInfo = async () => {
    const { data, error } = await supabase.auth.getUser();
    if (!error && data?.user) {
      setStudentId(data.user.id || null);
      setStudentEmail(data.user.email || null);


      const { data: profileData } = await supabase
        .from("student_profiles")
        .select("full_name")
        .eq("id", data.user.id)
        .single();

      if (profileData?.full_name) {
        setStudentName(profileData.full_name);
      }
    }
  };

  fetchStudentInfo();
}, []);

  useEffect(() => {
    const fetchSubscriptionPlans = async () => {
      const { data, error } = await supabase
        .from('subscription_plan')
        .select('id, name, price, sessions_count');

      if (error) {
        console.error("Error fetching subscription plans:", error);
      } else {
        setSubscriptionPlans(data || []);
      }
    };

    fetchSubscriptionPlans();
  }, []);
 
  useEffect(() => {
    const checkSubscription = async () => {
      if (!studentId) return;

      setLoadingSubscription(true);
      try {

        const { data, error } = await supabase
          .from("student_subscriptions")
          .select("*")
          .eq("student_id", studentId)
          .eq("status", "active")
          .gt("remaining_sessions", 0)
          .order("created_at", { ascending: false })
          .maybeSingle();

        if (error) {
          console.error("Subscription check failed:", error);
          setSubscription(null);
          return;
        }


        if (data) {
          setSubscription(data);
        } else {
          setSubscription(null);
        }
      } catch (err) {
        console.error("Error checking subscription:", err);
        setSubscription(null);
      } finally {
        setLoadingSubscription(false);
      }
    };

    if (studentId) {
      checkSubscription();
    }
  }, [studentId]);
  useEffect(() => {
    const fetchSubjects = async () => {
      const { data, error } = await supabase
        .from("subjects")
        .select("id, name");

      if (!error) setSubjects(data || []);
    };

    const fetchTutors = async () => {
      const { data, error } = await supabase
        .from("tutor_profiles")
        .select("id, full_name, calendly_username");

      if (!error) setTutors(data || []);
    };

    const fetchStudentId = async () => {
      const { data, error } = await supabase.auth.getUser();
      if (!error) setStudentId(data?.user?.id || null);
    };

    fetchSubjects();
    fetchTutors();
    fetchStudentId();
  }, []);

  useEffect(() => {
    const fetchTutorsBySubject = async () => {
      if (!selectedSubject) {
        setFilteredTutors([]);
        return;
      }

      setLoadingTutors(true);

      try {
        const { data: subjectData, error: subjectError } = await supabase
          .from("subjects")
          .select("id")
          .eq("name", selectedSubject)
          .single();

        if (subjectError) throw subjectError;

        const subjectId = subjectData.id;

        const { data: tutorSubjects, error: tutorError } = await supabase
          .from("tutors_subject")
          .select("tutor_id")
          .eq("subjects_id", subjectId);

        if (tutorError) throw tutorError;

        const tutorIds = tutorSubjects.map(ts => ts.tutor_id);
        const filtered = tutors.filter(tutor => tutorIds.includes(tutor.id));

        setFilteredTutors(filtered);
      } catch (error) {
        console.error("Error fetching tutors by subject:", error);
        toast({
          title: "Error",
          description: "Failed to load tutors for the selected subject.",
          variant: "destructive",
        });
        setFilteredTutors([]);
      } finally {
        setLoadingTutors(false);
      }
    };

    fetchTutorsBySubject();
  }, [selectedSubject, tutors]);

  useEffect(() => {
    if (!studentId) return;
    const channel = supabase.channel('realtime:student_sessions').on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'student_sessions',
          filter: `student_id=eq.${studentId}`
        },
        async (payload) => {
          if (pendingSubscriptionUse && subscription) {
            try {
              const { error } = await supabase
                .from("student_subscriptions")
                .update({
                  remaining_sessions: subscription.remaining_sessions - 1,
                  updated_at: new Date().toISOString()
                })
                .eq("id", subscription.id);

              if (!error) {
                setSubscription(prev => ({
                  ...prev!,
                  remaining_sessions: prev!.remaining_sessions - 1
                }));
              }
            } finally {
              setPendingSubscriptionUse(false);
            }
          }

          if (onBookingComplete) {
            onBookingComplete(payload.new);
          }
        }
      )
      .subscribe();
      return () => {
      supabase.removeChannel(channel); // Changed from subscription to channel
    };
  }, [studentId, pendingSubscriptionUse, subscription, onBookingComplete]);

  useEffect(() => {
    const paymentStatus = searchParams.get("payment");
    const subscriptionStatus = searchParams.get("subscription");
    const storedUrl = localStorage.getItem("schedulingUrl");

    if ((paymentStatus === "success" || subscriptionStatus === "success") && storedUrl) {
      setCalendlyUrl(storedUrl);
      localStorage.removeItem("schedulingUrl"); // Clean up after use

      window.history.replaceState({}, document.title, window.location.pathname);

      toast({
        title: paymentStatus === "success" ? "Payment Successful" : "Subscription Activated",
        description: "Please complete your booking with the selected time slot.",
      });
    }
  }, [searchParams, toast]);

  useEffect(() => {
    const handleCalendlyEvent = (e: MessageEvent) => {
      if (e.data?.event === "calendly.event_scheduled") {

        setTimeout(() => {
          handleBookingComplete();
        }, 3000);
      }
    };

    window.addEventListener("message", handleCalendlyEvent);

    return () => window.removeEventListener("message", handleCalendlyEvent);
  }, []);
  
  const convertToStudentTimezone = (isoString) => {
    const studentTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
    const date = new Date(isoString);

    return new Intl.DateTimeFormat("en-US", {
      timeZone: studentTimezone,
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
      second: "2-digit",
      hour12: true,
    }).format(date);
  };

  const handleBookingComplete = () => {

    setSelectedTutor(null);
    setTutorEventTypes([]);
    setAvailableTimes([]);
    setEventName(null);
    setCalendlyUrl(null);
    setLastBookedEventUri(null);
    setSelectedSubject(null);
    setBookingStep("subjects");


    if (onBookingComplete) {
      onBookingComplete();
    }
  };


  const fetchTutorEventTypes = async (tutorId) => {

    setTutorEventTypes([]);
    setAvailableTimes([]);
    setEventName(null);


    setLoadingEvents(true)
    try {
      const response = await fetch(`${SUPABASE_FUNCTIONS_URL}/get-tutor-events`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${import.meta.env.VITE_SUPABASE_ANON_KEY}`
        },
        body: JSON.stringify({ tutor_id: tutorId }),
      });
      const { collection } = await response.json();
      console.log({collection})
      setTutorEventTypes(collection || []);
      setSelectedTutor(tutors.find((tutor) => tutor.id === tutorId));

      // Find the event that matches the selected subject
      const matchingEvent = collection?.find(event => event.name === selectedSubject);

      if (matchingEvent) {
        // If we found a matching event, fetch its availability directly
        fetchEventAvailability(matchingEvent.uri);
      } else {
        // If no matching event, show all events
        setBookingStep("sessions");
      }
    } catch (error) {
      console.error("Error fetching tutor events:", error);
      setTutorEventTypes([]);
      toast({
        title: "Error",
        description: "Failed to load tutor's available sessions.",
        variant: "destructive",
      });
      setBookingStep("tutors");
    } finally {
      setLoadingEvents(false);
    }
  };

  const fetchEventAvailability = async (eventUri) => {
    setLoadingSlots(true);
    setCurrentPage(1);
    setBookingStep("times")

    try {
      const response = await fetch(`${SUPABASE_FUNCTIONS_URL}/get-event-availability`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${import.meta.env.VITE_SUPABASE_ANON_KEY}`
        },
        body: JSON.stringify({
          event_uri: eventUri,
          student_id: studentId
        }),
      });

      const { event_name, times } = await response.json();
      localStorage.setItem("lastBookedEventUri", eventUri);
      setEventName(event_name || "Unknown Event");
      setAvailableTimes(times || []);
      setLastBookedEventUri(eventUri);
      setBookingStep("times");
    } catch (error) {
      console.error("Error fetching event availability:", error);
      setEventName(null);
      setAvailableTimes([]);
      toast({
        title: "Error",
        description: "Failed to load available time slots.",
        variant: "destructive",
      });
    } finally {
      setLoadingSlots(false);
    }
  };

  const handleSelectPaymentModel = (model) => {

    setSelectedPricingModel(model);
    if (model === 'pay-per-session') {
      handleProcessPayment();
    } else {

      if (availableTimes && availableTimes.length > 0) {
        localStorage.setItem("schedulingUrl", availableTimes[0]?.scheduling_url);
      }

      window.location.href = '/subscriptions';
    }
  };

  const handleSelectSubscriptionPlan = (plan) => {
    setSelectedSubscriptionPlan(plan);
    handleProcessSubscription(plan);
  };


  const handleProcessSubscription = async (plan) => {
    if (!studentId) {
      toast({
        title: "Error",
        description: "Please log in first.",
        variant: "destructive",
      });
      return;
    }


    const schedulingUrl = localStorage.getItem("schedulingUrl");

    if (!schedulingUrl) {
      toast({
        title: "Error",
        description: "Missing scheduling information. Please try again.",
        variant: "destructive",
      });
      return;
    }

    const { data: userData } = await supabase.auth.getUser();
    const studentEmail = userData?.user?.email;

    try {
      const response = await fetch(
        `${SUPABASE_FUNCTIONS_URL}/create-stripe-subscription`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${import.meta.env.VITE_SUPABASE_ANON_KEY}`,
          },
          body: JSON.stringify({
            studentEmail,
            studentId,
            planId: plan.id,
            planName: plan.name,
            planPriceId: plan.price,
            tutorId: selectedTutor?.id,
            planSessions: plan.sessions_count,
            schedulingUrl: schedulingUrl,
          }),
        }
      );

      const jsonResponse = await response.json();
      const { url, error } = jsonResponse;

      if (error) {
        throw new Error(error);
      }

      if (url) window.location.href = url;
    } catch (error) {
      console.error("Error creating subscription session:", error);
      toast({
        title: "Error",
        description: "Failed to process payment. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleProcessPayment = async () => {
    if (!lastBookedEventUri) {
      toast({
        title: "Error",
        description: "Invalid time slot selected.",
        variant: "destructive",
      });
      return;
    }

    if (!studentId) {
      toast({
        title: "Error",
        description: "Please log in first.",
        variant: "destructive",
      });
      return;
    }


    const schedulingUrl = localStorage.getItem("schedulingUrl");

    if (!schedulingUrl) {
      toast({
        title: "Error",
        description: "Missing scheduling information. Please try again.",
        variant: "destructive",
      });
      return;
    }

    const { data: userData } = await supabase.auth.getUser();
    const studentEmail = userData?.user?.email;

    const response = await fetch(
      `${SUPABASE_FUNCTIONS_URL}/create-stripe-session`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${import.meta.env.VITE_SUPABASE_ANON_KEY}`,
        },
        body: JSON.stringify({
          tutorId: selectedTutor?.id,
          tutorName: selectedTutor?.full_name,
          studentEmail,
          studentId,
          eventUri: lastBookedEventUri,
          eventId: lastBookedEventUri.split("/").pop(),
          schedulingUrl: schedulingUrl,
          subjectName: selectedSubject,
        }),
      }
    );

    const { url } = await response.json();
    if (url) window.location.href = url;
  };

  const isSameTimestamp = (date1String, date2String) => {
    try {
      const date1 = new Date(date1String);
      const date2 = new Date(date2String);
      return Math.abs(date1.getTime() - date2.getTime()) < 5000;
    } catch (e) {
      console.error("Date comparison error:", e);
      return false;
    }
  };

  const normalizeUrl = (url) => {
    try {
      return decodeURIComponent(url.trim().replace(/\/+$/, ""));
    } catch {
      return url;
    }
  };

  const handleBook = async (eventUri, slot) => {
    if (!studentId) {
      toast({
        title: "Error",
        description: "Please log in first.",
        variant: "destructive",
      });
      return;
    }

    try {
      toast({
        title: "Checking",
        description: "Verifying payment status...",
      });

      const timeFromSchedulingUrl = slot.scheduling_url.split('/').pop();
      const eventId = eventUri.split('/').pop();

      console.log('studentID ',studentId)


      const { data: paymentsData, error: paymentError } = await supabase
        .from("payments")
        .select("*")
        .eq("student_id", studentId)
        .eq("status", "paid");

      if (paymentError) {
        console.error("Error checking payment status:", paymentError);
        throw paymentError;
      }

      console.log("Checking for scheduling URL:", slot.scheduling_url);
      console.log("Returned payment records:", paymentsData);


      paymentsData.forEach(p =>
        console.log("Stored scheduling_url in DB:", p.scheduling_url)
      );


      const matchingPayment = paymentsData?.find((payment) => {
        if (payment.scheduling_url === slot.scheduling_url) {
          console.log("Found exact URL match");
          return true;
        }

        if (payment.scheduling_url) {
          try {
            const paymentTimeStr = payment.scheduling_url.split('/').pop();
            console.log("Comparing times:", paymentTimeStr, timeFromSchedulingUrl);
            if (
              paymentTimeStr &&
              new Date(paymentTimeStr).getTime() === new Date(timeFromSchedulingUrl).getTime()
            ) {
              console.log(" Found timestamp match");
              return true;
            }
          } catch (e) {
            console.error("Error comparing timestamps:", e);
          }
        }

        return false;
      });

      console.log("Matching payment found:", matchingPayment);


      if (matchingPayment) {
        localStorage.setItem("schedulingUrl", slot.scheduling_url);
        setCalendlyUrl(slot.scheduling_url);
        toast({
          title: "Already Paid",
          description: "This session has already been paid for. Please complete your booking.",
        });
        return;
      }


      if (subscription && subscription.remaining_sessions > 0) {
        handleDirectBookWithSubscription(slot);
      } else {
        localStorage.setItem("schedulingUrl", slot.scheduling_url);
        setSelectedPricingModel(null);
        setSelectedSubscriptionPlan(null);
        setBookingStep("pricing");
      }
    } catch (error) {
      console.error("Error checking payment status:", error);
      toast({
        title: "Error",
        description: "Failed to verify payment status. Please try again.",
        variant: "destructive",
      });
    }
  };


  const handleDirectBookWithSubscription = (slot) => {

    setCalendlyUrl(slot.scheduling_url);


    setPendingSubscriptionUse(true);

    toast({
      title: "Using Subscription",
      description: `Using 1 session from your subscription (${subscription.remaining_sessions} remaining)`,
    });
  };

  const handleSubjectSelect = (subject) => {
    setSelectedSubject(subject);
    setBookingStep("tutors");
  };

  const handleSelectTutor = (tutor) => {
    setSelectedTutor(tutor);

    fetchTutorEventTypes(tutor.id);
    setBookingStep("sessions");
  };

  const handleBackButton = () => {
    if (bookingStep === "pricing") {
      setBookingStep("times");
    } else if (bookingStep === "subscription") {
      setBookingStep("pricing");
    } else if (bookingStep === "times") {
      setBookingStep("tutors");
    } else if (bookingStep === "sessions") {
      setBookingStep("tutors");
    } else if (bookingStep === "tutors") {
      setBookingStep("subjects");
      setSelectedSubject(null);
    }
  };

  const renderSubscriptionStatus = () => {
    if (loadingSubscription) {
      return (
        <div className="flex items-center space-x-2 text-sage-600 mb-4 bg-sage-50 p-2 rounded">
          <svg className="animate-spin h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v4a4 4 0 00-4 4H4z"></path>
          </svg>
          <span>Checking subscription status...</span>
        </div>
      );
    }

    if (subscription) {
      return (
        <div className="bg-green-50 text-green-800 rounded-md p-3 mb-4 flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
          </svg>
          <span>
            Active subscription: <strong>{subscription.remaining_sessions} sessions</strong> remaining
          </span>
        </div>
      );
    }

    return null;
  };

  return (
    <Card className="p-6 shadow-md border border-sage-100">
      <h2 className="mb-2 text-xl font-semibold text-sage-800">Book a Session</h2>

      {renderSubscriptionStatus()}



{calendlyUrl ? (
  <>
    <Button
      className="mb-4"
      variant="outline"
      onClick={() => {
        setCalendlyUrl(null);

        setBookingStep("subjects");
      }}
    >
      <ChevronLeft className="w-4 h-4 mr-2" /> Back to Time Slots
    </Button>

    <InlineWidget
      url={calendlyUrl}
      pageSettings={{ hideEventTypeDetails: true }}
      styles={{ height: '700px' }}
      utm={{ utmCampaign: 'booking' }}
      prefill={{
        email: studentEmail,
        name: studentName,
      }}
    />
  </>
) : (
        <>
          {bookingStep !== "subjects" && (
            <Button
              className="mb-4"
              variant="outline"
              onClick={handleBackButton}
            >
              <ChevronLeft className="w-4 h-4 mr-2" /> Back
            </Button>
          )}


          {bookingStep === "subjects" && (
            <div className="mb-6">
              <h3 className="text-md font-medium text-sage-700 mb-2">Select a Subject</h3>
              <div className="flex overflow-x-auto pb-4 -mx-2">
                <div className="flex space-x-3 px-2">
                  {subjects.map((subject) => (
                    <div
                      key={subject.id}
                      className={`flex-shrink-0 border rounded-lg p-4 cursor-pointer transition-colors ${
                        selectedSubject === subject.name
                          ? 'border-sage-600 bg-sage-50'
                          : 'border-sage-200 hover:border-sage-400'
                      }`}
                      onClick={() => handleSubjectSelect(subject.name)}
                    >
                      <h4 className="font-medium text-sage-800 whitespace-nowrap">{subject.name}</h4>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}


          {bookingStep === "tutors" && (
            <div className="mb-6">
              <h3 className="text-md font-medium text-sage-700 mb-2">Choose a Tutor for {selectedSubject}</h3>
              <br></br>
              {loadingTutors ? (
                <div className="flex justify-center items-center gap-2">
                  <svg
                    className="w-5 h-5 animate-spin text-green-700"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                    ></circle>
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8v4a4 4 0 00-4 4H4z"
                    ></path>
                  </svg>
                  <p>Loading tutors...</p>
                </div>
              ) : filteredTutors.length === 0 ? (
                <p>No tutors found for this subject.</p>
              ) : (
                <div className="grid grid-cols-1 gap-3">
                  {filteredTutors.map((tutor) => (
                    <Card
                      key={tutor.id}
                      className="p-4 cursor-pointer hover:bg-sage-50"
                      onClick={() => handleSelectTutor(tutor)}
                    >
                      <p className="font-medium">{tutor.full_name}</p>
                    </Card>
                  ))}
                </div>
              )}
            </div>
          )}
          
          {bookingStep === "sessions" && (
            <div className="mb-6">
              {/* <h3 className="text-md font-medium text-sage-700 mb-2">
                Choose a Session with {selectedTutor?.full_name}
              </h3> */}
              {loadingEvents ? (
                <div className="flex justify-center items-center gap-2">
                  <svg
                    className="w-5 h-5 animate-spin text-green-700"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                    ></circle>
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8v4a4 4 0 00-4 4H4z"
                    ></path>
                  </svg>
                  <p>Loading sessions...</p>
                </div>
              ) : tutorEventTypes.length === 0 ? (
                <p className="text-sage-600">No sessions available for this tutor.</p>
              ) : (
                <div className="grid grid-cols-1 gap-3">
                  {tutorEventTypes.filter((event) => event.name === selectedSubject).map((event) => (
                    <Card
                      key={event.uri}
                      className="p-4 cursor-pointer hover:bg-sage-50"
                      onClick={() => fetchEventAvailability(event.uri)}
                    >
                      <h4 className="text-sage-800 font-medium">{event.name}</h4>
                      <p className="text-sm text-sage-600">{event.description}</p>
                    </Card>
                  ))}
                </div>
              )}
            </div>
          )}


          {bookingStep === "times" && (
            <div className="mb-6">
              <h3 className="text-md font-medium text-sage-700 mb-2">Choose a Time Slot for {eventName}</h3>
              {loadingSlots ? (
                <div className="flex justify-center items-center gap-2">
                  <svg
                    className="w-5 h-5 animate-spin text-green-700"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                    ></circle>
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8v4a4 4 0 00-4 4H4z"
                    ></path>
                  </svg>
                  <p>Loading time slots...</p>
                </div>
              ) : availableTimes.length === 0 ? (
                <p>No available time slots.</p>
              ) : (
                <div className="space-y-3">
                  {currentSlots.map((slot, index) => (
                    <Card
                      key={index}
                      className="p-4 flex justify-between items-center"
                    >
                      <p>{convertToStudentTimezone(slot.start_time)}</p>
                      <Button
                        variant="success"
                        onClick={() => handleBook(lastBookedEventUri, slot)}
                      >
                        {subscription && subscription.remaining_sessions > 0 ? "Book with Subscription" : "Book"}
                      </Button>
                    </Card>
                  ))}
                  {totalPages > 1 && (
                    <div className="flex justify-between mt-4">
                      <Button
                        variant="outline"
                        onClick={() => setCurrentPage((p) => Math.max(p - 1, 1))}
                        disabled={currentPage === 1}
                      >
                        <ChevronLeft className="w-4 h-4 mr-2" />
                        Previous
                      </Button>
                      <Button
                        variant="outline"
                        onClick={() => setCurrentPage((p) => Math.min(p + 1, totalPages))}
                        disabled={currentPage === totalPages}
                      >
                        Next
                        <ChevronRight className="w-4 h-4 ml-2" />
                      </Button>
                    </div>
                  )}
                </div>
              )}
            </div>
          )}


          {bookingStep === "pricing" && (
            <div className="mb-6">
              <h3 className="text-md font-medium text-sage-700 mb-4">Select Pricing Model</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Card
                  className="p-6 border cursor-pointer hover:bg-sage-50 transition-colors"
                  onClick={() => handleSelectPaymentModel('pay-per-session')}
                >
                  <h4 className="font-medium text-sage-800 text-lg mb-2">Pay-per-Session</h4>
                  <p className="text-sage-600">Pay for individual sessions as you go.</p>
                  <p className="text-sage-800 font-medium mt-2">$70 per session</p>
                </Card>

                <Card
                  className="p-6 border cursor-pointer hover:bg-sage-50 transition-colors"
                  onClick={() => handleSelectPaymentModel('subscription')}
                >
                  <h4 className="font-medium text-sage-800 text-lg mb-2">Subscription Plan</h4>
                  <p className="text-sage-600">Save with our discounted multi-session packages.</p>
                  <p className="text-sage-800 font-medium mt-2">Starting at $45/session</p>
                </Card>
              </div>
            </div>
          )}


          {bookingStep === "subscription" && (
            <div className="mb-6">
              <h3 className="text-md font-medium text-sage-700 mb-4">Choose a Subscription Plan</h3>
              <div className="overflow-x-auto">
                <table className="w-full border-collapse">
                  <thead>
                    <tr className="bg-sage-100">
                      <th className="py-2 px-4 border text-left">Plan</th>
                      <th className="py-2 px-4 border text-left">Price</th>
                       <th className="py-2 px-4 border text-left">Sessions</th>

                      <th className="py-2 px-4 border text-left">Action</th>
                    </tr>
                  </thead>
                  <tbody>
                    {subscriptionPlans.map((plan) => (
                      <tr key={plan.id} className="border-b hover:bg-sage-50">
                        <td className="py-3 px-4 border">{plan.name}</td>
                        <td className="py-3 px-4 border">${plan.price}</td>
                        <td className="py-3 px-4 border">{plan.sessions_count}</td>

                        <td className="py-3 px-4 border">
                          <Button
                            variant="success"
                            size="sm"
                            onClick={() => handleSelectSubscriptionPlan(plan)}
                          >
                            Select
                          </Button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}
        </>
      )}
    </Card>
  );
};

export default BookSession;