import { useEffect, useState } from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Calendar, XCircle } from "lucide-react";
import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON><PERSON>Header,
  <PERSON><PERSON>Footer,
  DialogTitle,
} from "@/components/ui/dialog";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/components/ui/use-toast";

interface UpcomingSessionsProps {
  refreshTrigger: number;
  onUpcomingCountChange?: (count: number) => void;
}

const UpcomingSessions = ({ refreshTrigger, onUpcomingCountChange }: UpcomingSessionsProps) => {
  const [sessions, setSessions] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [showCancelModal, setShowCancelModal] = useState(false);
  const [selectedSession, setSelectedSession] = useState<any>(null);
  const [viewType, setViewType] = useState("upcoming");
  const [currentPage, setCurrentPage] = useState(1);
  const sessionsPerPage = 4;
  const { toast } = useToast();

  

  const fetchSessions = async () => {
    setLoading(true);
  
    const {
      data: { user },
    } = await supabase.auth.getUser();
    if (!user) {
      console.error("No user logged in");
      setLoading(false);
      return;
    }
  
    const { data: studentProfile, error: studentError } = await supabase
      .from("student_profiles")
      .select("id")
      .eq("email", user.email)
      .single();
  
    if (studentError || !studentProfile) {
      console.error(" Student profile not found for email:", user.email, studentError);
      setLoading(false);
      return;
    }
  
    const { data, error } = await supabase
      .from("student_sessions")
      .select("*")
      .eq("student_id", studentProfile.id)
      .order("start_time", { ascending: true });
  
    if (error) {
      console.error(" Error fetching sessions:", error);
    } else {
      const sortedData = (data || []).sort((a, b) => {
        if (a.status === "active" && b.status !== "active") return -1;
        if (a.status !== "active" && b.status === "active") return 1;
        return new Date(a.start_time).getTime() - new Date(b.start_time).getTime();
      });
      setSessions(sortedData);
  
      
      if (onUpcomingCountChange) {
        const upcomingCount = sortedData.filter(
          (s) => s.status !== "canceled" && new Date(s.end_time) > new Date()
        ).length;
        onUpcomingCountChange(upcomingCount);
      }
    }
  
    setLoading(false);
  };
  

  useEffect(() => {
    fetchSessions();
  }, [refreshTrigger]);

  const handleCancelClick = (session: any) => {
    setSelectedSession(session);
    setShowCancelModal(true);
  };

  const confirmCancelSession = async () => {
    if (!selectedSession) return;

    try {
      const { error } = await supabase
        .from("student_sessions")
        .update({
          status: "canceled",
        })
        .eq("id", selectedSession.id);

      if (error) {
        console.error("Error cancelling session:", error);
        toast({ title: "Error", description: "Failed to cancel session.", variant: "destructive" });
      } else {
        
        toast({ title: "Success", description: "Session cancelled successfully." });
        
        setSessions((prev) =>
          prev.map((s) =>
            s.id === selectedSession.id ? { ...s, status: "canceled" } : s
          )
        );
      }
    } catch (err) {
      console.error("Error during cancellation:", err);
      toast({ title: "Error", description: "Failed to cancel session.", variant: "destructive" });
    } finally {
      setShowCancelModal(false);
      setSelectedSession(null);
    }
  };

  const filteredSessions = () => {
    if (viewType === "all") return sessions;
    if (viewType === "upcoming") {
      return sessions.filter(
        (s) => s.status !== "canceled" && new Date(s.end_time) > new Date()
      );
    }
    if (viewType === "canceled") {
      return sessions.filter((s) => s.status === "canceled");
    }
    if (viewType === "past") {
      return sessions.filter(
        (s) => s.status !== "canceled" && new Date(s.end_time) < new Date()
      );
    }
    return [];
  };

  const paginatedSessions = () => {
    const data = filteredSessions();
    const startIndex = (currentPage - 1) * sessionsPerPage;
    return data.slice(startIndex, startIndex + sessionsPerPage);
  };

  const totalPages = Math.ceil(filteredSessions().length / sessionsPerPage);

  
  const getViewTitle = () => {
    switch (viewType) {
      case "upcoming": return "Upcoming Sessions";
      case "canceled": return "Canceled Sessions";
      case "past": return "Past Sessions";
      case "all": return "All Sessions";
      default: return "Sessions";
    }
  };

  
  const localTimeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;

  const formatDateLocal = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString(undefined, {
      year: "numeric",
      month: "short",
      day: "numeric",
      timeZone: localTimeZone,
    });
  };
  
  const formatTimeLocal = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString(undefined, {
      hour: "2-digit",
      minute: "2-digit",
      hour12: true,
      timeZone: localTimeZone,
    }) + ` (${localTimeZone})`;
  };
  

  return (
    <>
      <Card className="p-6">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold text-sage-800">{getViewTitle()}</h2>
          {viewType === "upcoming" && (
            <span className="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded">
              Active
            </span>
          )}
        </div>

        
        <div className="mb-6">
          <div className="flex flex-wrap gap-3 mb-4">
            <Button
              variant={viewType === "upcoming" ? "default" : "outline"}
              className={`transition-all shadow hover:shadow-md rounded-xl px-4 py-2 ${
                viewType === "upcoming" ? "bg-sage-600 text-white" : ""
              }`}
              onClick={() => {
                setViewType("upcoming");
                setCurrentPage(1);
              }}
            >
              Upcoming
            </Button>
            <Button
              variant={viewType === "past" ? "default" : "outline"}
              className={`transition-all shadow hover:shadow-md rounded-xl px-4 py-2 ${
                viewType === "past" ? "bg-sage-600 text-white" : ""
              }`}
              onClick={() => {
                setViewType("past");
                setCurrentPage(1);
              }}
            >
              Past
            </Button>
            <Button
              variant={viewType === "canceled" ? "default" : "outline"}
              className={`transition-all shadow hover:shadow-md rounded-xl px-4 py-2 ${
                viewType === "canceled" ? "bg-sage-600 text-white" : ""
              }`}
              onClick={() => {
                setViewType("canceled");
                setCurrentPage(1);
              }}
            >
              Canceled
            </Button>
            <Button
              variant={viewType === "all" ? "default" : "outline"}
              className={`transition-all shadow hover:shadow-md rounded-xl px-4 py-2 ${
                viewType === "all" ? "bg-sage-600 text-white" : ""
              }`}
              onClick={() => {
                setViewType("all");
                setCurrentPage(1);
              }}
            >
              All Sessions
            </Button>
          </div>
        </div>

        {loading ? (
          <p className="text-center text-sage-600">Loading...</p>
        ) : paginatedSessions().length === 0 ? (
          <div className="flex flex-col items-center justify-center py-6 text-sage-600">
            <Calendar className="mb-2 h-12 w-12" />
            <p>No {viewType} sessions found</p>
          </div>
        ) : (
          <div className="space-y-4">
            {paginatedSessions().map((session) => (
              <div
                key={session.id}
                className="flex items-center justify-between rounded-lg bg-white p-4 shadow-sm border border-sage-200"
              >
                <div>
                  <p className="font-medium text-sage-800 text-lg mb-1">{session.event_name}</p>
                  <p className="text-sm text-sage-600 mb-0.5">Tutor: {session.tutor_name}</p>
                  <p className="text-sm text-sage-500">
                    {formatDateLocal(session.start_time)}
                  </p>
                  <p className="text-sm text-sage-500">
                    {formatTimeLocal(session.start_time)} - {formatTimeLocal(session.end_time)}
                  </p>
                  <p
                    className={`text-sm mt-1 font-semibold ${
                      session.status === "canceled"
                        ? "text-red-500"
                        : session.status === "completed"
                        ? "text-gray-500"
                        : "text-green-600"
                    }`}
                  >
                    Status: {session.status.charAt(0).toUpperCase() + session.status.slice(1)}
                  </p>
                </div>
                {session.status === "active" && new Date(session.end_time) > new Date() && (
                  <div className="flex gap-2">
                    <Button
                      variant="fadeRed"
                      size="sm"
                      onClick={() => handleCancelClick(session)}
                    >
                      Cancel
                    </Button>
                    {session.meeting_url ? (
                      <a
                        href={session.meeting_url}
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        <Button variant="outline" size="sm" className="text-sage-600">
                          Join
                        </Button>
                      </a>
                    ) : (
                      <Button variant="secondary" size="sm" disabled>
                        No Link
                      </Button>
                    )}
                  </div>
                )}
              </div>
            ))}

            {filteredSessions().length > sessionsPerPage && (
              <div className="flex justify-between mt-4">
                <Button
                  disabled={currentPage === 1}
                  onClick={() => setCurrentPage((prev) => prev - 1)}
                >
                  Prev
                </Button>
                <span className="text-sm text-sage-600 self-center">
                  Page {currentPage} of {totalPages}
                </span>
                <Button
                  disabled={currentPage === totalPages || totalPages === 0}
                  onClick={() => setCurrentPage((prev) => prev + 1)}
                >
                  Next
                </Button>
              </div>
            )}
          </div>
        )}
      </Card>

      <Dialog open={showCancelModal} onOpenChange={setShowCancelModal}>
        <DialogContent>
          <DialogHeader>
            <div className="flex items-center gap-2">
              <XCircle className="text-red-500 h-6 w-6" />
              <DialogTitle className="text-lg font-semibold">Cancel Session</DialogTitle>
            </div>
          </DialogHeader>
          <p>Are you sure you want to cancel this session?</p>
          <DialogFooter className="mt-4 flex gap-3">
            <Button variant="destructive" onClick={confirmCancelSession}>
              Yes, Cancel
            </Button>
            <Button variant="outline" onClick={() => setShowCancelModal(false)}>
              No
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default UpcomingSessions;