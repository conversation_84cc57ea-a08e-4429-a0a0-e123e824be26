import { Card } from "@/components/ui/card";
import { Book, Calendar, MessageSquare, GraduationCap } from "lucide-react";
import { useNavigate } from "react-router-dom";

interface StudentStatsProps {
  upcomingCount: number;
}

const StudentStats = ({ upcomingCount }: StudentStatsProps) => {
  const navigate = useNavigate();

  return (
    <div className="mb-8 grid gap-6 sm:grid-cols-2 lg:grid-cols-4">
      <Card 
        className="cursor-pointer p-6 transition-colors hover:bg-sage-100"
        onClick={() => navigate("/student/active-courses")}
      >
        <div className="flex items-center gap-4">
          <div className="rounded-full bg-sage-100 p-3">
            <Book className="h-6 w-6 text-sage-600" />
          </div>
          <div>
            <p className="text-sm text-sage-600">Active Courses</p>
            <p className="text-2xl font-bold text-sage-800">4</p>
          </div>
        </div>
      </Card>
      <Card className="p-6">
        <div className="flex items-center gap-4">
          <div className="rounded-full bg-sage-100 p-3">
            <Calendar className="h-6 w-6 text-sage-600" />
          </div>
          <div>
            <p className="text-sm text-sage-600">Upcoming Sessions</p>
            <p className="text-2xl font-bold text-sage-800">{upcomingCount}</p>
          </div>
        </div>
      </Card>
      <Card 
        className="cursor-pointer p-6 transition-colors hover:bg-sage-100"
        onClick={() => navigate("/messages")}
      >
        <div className="flex items-center gap-4">
          <div className="rounded-full bg-sage-100 p-3">
            <MessageSquare className="h-6 w-6 text-sage-600" />
          </div>
          <div>
            <p className="text-sm text-sage-600">Messages</p>
            <p className="text-2xl font-bold text-sage-800">5</p>
          </div>
        </div>
      </Card>
      <Card 
        className="cursor-pointer p-6 transition-colors hover:bg-sage-100"
        onClick={() => navigate("/student/completed-courses")}
      >
        <div className="flex items-center gap-4">
          <div className="rounded-full bg-sage-100 p-3">
            <GraduationCap className="h-6 w-6 text-sage-600" />
          </div>
          <div>
            <p className="text-sm text-sage-600">Completed Courses</p>
            <p className="text-2xl font-bold text-sage-800">6</p>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default StudentStats;