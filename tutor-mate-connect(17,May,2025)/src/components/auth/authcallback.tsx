import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { supabase } from "@/integrations/supabase/client";
import { LucideJoystick } from "lucide-react";

const AuthCallback = () => {
  const navigate = useNavigate();
  const [status, setStatus] = useState({
    message: "Processing login...",
    description: "Please wait while we authenticate your account.",
    isError: false
  });

  useEffect(() => {
    let isMounted = true;
  
    const handleAuthRedirect = async () => {
      try {
        const hashParams = new URLSearchParams(window.location.hash.slice(1));
        const accessToken = hashParams.get("access_token");
        const refreshToken = hashParams.get("refresh_token");
  
        if (!accessToken || !refreshToken) {
          if (isMounted) setStatus({
            message: "Authentication Failed",
            description: "Missing authentication tokens. Please try logging in again.",
            isError: true
          });
          setTimeout(() => isMounted && navigate("/login"), 2000);
          return;
        }
  
        const { error: sessionError } = await supabase.auth.setSession({
          access_token: accessToken,
          refresh_token: refreshToken,
        });
        if (sessionError) throw new Error(`Session setup error: ${sessionError.message}`);
  
        const { data: userData, error: userError } = await supabase.auth.getUser();
        if (userError || !userData?.user) throw new Error("Failed to fetch user data");
  
        const { email, id, user_metadata } = userData.user;
        const fullName = user_metadata?.full_name || email?.split('@')[0] || "User";
  
        
        const { data: tutorProfile } = await supabase.from("tutor_profiles").select("*").eq("email", email).maybeSingle();
        const { data: studentProfile } = await supabase.from("student_profiles").select("*").eq("email", email).maybeSingle();
  
        let userRole = null;
  
        if (tutorProfile) {
          userRole = "tutor";
          localStorage.setItem("userRole", "tutor"); 
          
          setStatus({ message: "Login successful!", description: `Welcome back, ${tutorProfile.full_name}!`, isError: false });
          
          navigate("/tutor-dashboard")
        } else if (studentProfile) {
          userRole = "student";
          localStorage.setItem("userRole", "student"); 
          
          setStatus({ message: "Login successful!", description: `Welcome back, ${studentProfile.full_name}!`, isError: false });
          setTimeout(() => navigate("/student-dashboard"), 1000);
        } else {
          setStatus({ message: "Setting up your account...", description: "Creating your profile.", isError: false });
  
          
          userRole = localStorage.getItem("userRole") || "student";
          localStorage.setItem("userRole", userRole); 
          
  
          const profileTable = userRole === "tutor" ? "tutor_profiles" : "student_profiles";
          const { error: profileError } = await supabase.from(profileTable).upsert({ id, email, full_name: fullName });
          if (profileError) throw new Error(`Profile creation failed: ${profileError.message}`);
  
          setStatus({ message: "Account Created", description: `${fullName} your profile is ready!`, isError: false });
          setTimeout(() => navigate(`/${userRole}-dashboard`), 1500);
        }
  
        
      } catch (err) {
        console.error("Authentication error:", err);
        if (isMounted) {
          setStatus({ message: "Authentication Failed", description: err.message || "Unknown error occurred", isError: true });
          setTimeout(() => navigate("/login"), 2000);
        }
      }
    };
  
    handleAuthRedirect();
    return () => { isMounted = false; };
  }, [navigate]);
  

  return (
    <div className="flex flex-col items-center justify-center min-h-screen p-6 bg-gray-50">
      <div className="w-full max-w-md p-8 space-y-6 bg-white rounded-xl shadow-md">
        <div className="text-center">
          <h1 className={`text-2xl font-bold ${status.isError ? 'text-red-600' : 'text-blue-600'}`}>{status.message}</h1>
          <p className="mt-2 text-gray-600">{status.description}</p>
        </div>
        {!status.isError && <div className="w-12 h-12 border-4 border-blue-600 border-t-transparent rounded-full animate-spin"></div>}
      </div>
    </div>
  );
};

export default AuthCallback; 
