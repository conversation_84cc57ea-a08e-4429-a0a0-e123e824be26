import { Button } from "@/components/ui/button";
import { Mail } from "lucide-react";

interface SocialLoginProps {
  onGoogleLogin: () => void;
  loading: boolean;
}

export const SocialLogin = ({ onGoogleLogin, loading }: SocialLoginProps) => {
  return (
    <Button 
      variant="outline" 
      className="w-full" 
      onClick={onGoogleLogin}
      disabled={loading}
    >
      <Mail className="mr-2 h-4 w-4" />
      Continue with Google
    </Button>
  );
};