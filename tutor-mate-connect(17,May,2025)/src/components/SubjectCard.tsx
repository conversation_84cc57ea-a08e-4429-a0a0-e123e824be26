import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";

interface SubjectCardProps {
  subject: string;
  tutorCount: number;
  description: string;
  topics?: string[];
  difficulty?: string;
  delay?: number;
  className?: string;
}

export const SubjectCard = ({
  subject,
  tutorCount,
  description,
  topics,
  className,
}: SubjectCardProps) => {
  
  const [category] = description.split(' - ');
  
  return (
    <Card className={cn(
      "group cursor-pointer transition-all duration-300 hover:shadow-lg",
      className
    )}>
      <div className="p-6">
        <p className="mb-1 text-sm font-medium uppercase text-sage-600">{category}</p>
        <h3 className="mb-4 text-xl font-semibold">{subject}</h3>
        
        {topics && topics.length > 0 && (
          <div className="mb-4">
            <p className="mb-2 text-xs font-semibold uppercase text-sage-500">Key Topics</p>
            <div className="flex flex-wrap gap-2">
              {topics.map((topic) => (
                <Badge
                  key={topic}
                  variant="secondary"
                  className="bg-sage-50 text-xs"
                >
                  {topic}
                </Badge>
              ))}
            </div>
          </div>
        )}
        
        <div className="mt-4">
          <Badge variant="secondary" className="bg-sage-50">
            {tutorCount} {tutorCount === 1 ? 'Tutor' : 'Tutors'}
          </Badge>
        </div>
      </div>
    </Card>
  );
};