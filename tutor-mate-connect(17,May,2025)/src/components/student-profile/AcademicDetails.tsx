import { useStudentProfile } from "@/contexts/StudentProfileContext";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Plus, X } from "lucide-react";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";



export const AcademicDetails = () => {
  const { profileData, setProfileData } = useStudentProfile();

  const handleArrayChange = (field: string, value: string) => {
    if (!value.trim()) return;
    setProfileData({
      ...profileData,
      [field]: [...(profileData[field] || []), value],
    });
  };

  const removeArrayItem = (field: string, index: number) => {
    setProfileData({
      ...profileData,
      [field]: profileData[field].filter((_: string, i: number) => i !== index),
    });
  };

  const handleGradeChange = (value: string) => {
    setProfileData({ ...profileData, grade_level: value });
  };

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h3 className="text-lg font-semibold">Academic Details</h3>
        <p className="text-sm text-muted-foreground">
          Tell us about your academic background and needs.
        </p>
      </div>

      <div className="space-y-4">
      <div>
    <Label htmlFor="grade_level">Current Grade Level</Label>
    <Select value={profileData.grade_level} onValueChange={handleGradeChange}>
      <SelectTrigger>
        <SelectValue placeholder="Select your grade level" />
      </SelectTrigger>
      <SelectContent>
        {["6th Grade", "7th Grade", "8th Grade", "9th Grade", "10th Grade", "11th Grade", "12th Grade", "College"].map((grade) => (
          <SelectItem key={grade} value={grade}>
            {grade}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  </div>

          <div>
            <Label>Subjects Need Help With</Label>
            <div className="space-y-2">
              <div className="flex gap-2">
                <Input
                  placeholder="Add Subjects"
                  onKeyPress={(e) => {
                    if (e.key === 'Enter') {
                      e.preventDefault();
                      handleArrayChange('subjects', e.currentTarget.value);
                      e.currentTarget.value = '';
                    }
                  }}
                />
                <Button
                  type="button"
                  variant="outline"
                  size="icon"
                  onClick={(e) => {
                    const input = e.currentTarget.previousElementSibling as HTMLInputElement;
                    handleArrayChange('subjects', input.value);
                    input.value = '';
                  }}
                >
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
              <div className="flex flex-wrap gap-2">
                {profileData.subjects?.map((sub: string, index: number) => (
                  <div
                    key={index}
                    className="flex items-center gap-1 rounded-full bg-muted px-3 py-1 text-sm"
                  >
                    {sub}
                    <button
                      type="button"
                      onClick={() => removeArrayItem('subjects', index)}
                      className="ml-1 text-muted-foreground hover:text-foreground"
                    >
                      <X className="h-3 w-3" />
                    </button>
                  </div>
                ))}
              </div>
            </div>
          </div>
      </div>
    </div>
  );
};