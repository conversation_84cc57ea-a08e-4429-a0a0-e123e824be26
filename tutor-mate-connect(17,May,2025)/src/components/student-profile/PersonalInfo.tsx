import { useStudentProfile } from "@/contexts/StudentProfileContext";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Trash2, Upload } from "lucide-react";
import { useState } from "react";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";

export const PersonalInfo = () => {
  const { profileData, setProfileData } = useStudentProfile();
  const { toast } = useToast();
  const [isUploading, setIsUploading] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  const handleChange = (e) => {
    setProfileData({ ...profileData, [e.target.name]: e.target.value });
  };

  const handleFileUpload = async (e) => {
    if (!e.target.files || e.target.files.length === 0) return;

    try {
      setIsUploading(true);
      const file = e.target.files[0];
      const fileExt = file.name.split(".").pop();
      const { data: { user } } = await supabase.auth.getUser();

      if (!user) throw new Error("No user found");

      const fileName = `${user.id}/${Date.now()}.${fileExt}`;
      const { error: uploadError } = await supabase.storage.from("profile_photos").upload(fileName, file, { upsert: true });

      if (uploadError) throw uploadError;

      const { data: { publicUrl } } = supabase.storage.from("profile_photos").getPublicUrl(fileName);

      const { error: updateError } = await supabase.from("profiles").update({ profile_photo_url: publicUrl }).eq("id", user.id);

      if (updateError) throw updateError;

      setProfileData({ ...profileData, profile_photo_url: publicUrl });

      toast({ title: "Success", description: "Profile photo updated successfully" });
    } catch (error) {
      toast({ title: "Error", description: "Failed to upload profile photo", variant: "destructive" });
    } finally {
      setIsUploading(false);
    }
  };

  const handleDeletePhoto = async () => {
    try {
      setIsDeleting(true);
      const { data: { user } } = await supabase.auth.getUser();

      if (!user) throw new Error("No user found");

      const { error: updateError } = await supabase.from("profiles").update({ profile_photo_url: null }).eq("id", user.id);

      if (updateError) throw updateError;

      setProfileData({ ...profileData, profile_photo_url: null });

      toast({ title: "Success", description: "Profile photo removed successfully" });
    } catch (error) {
      toast({ title: "Error", description: "Failed to remove profile photo", variant: "destructive" });
    } finally {
      setIsDeleting(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h3 className="text-lg font-semibold">Personal Information</h3>
        <p className="text-sm text-muted-foreground">Let's get to know you better! Fill in your basic information.</p>
      </div>
      <div className="space-y-4">
        <div>
          <Label htmlFor="full_name">Full Name</Label>
          <Input id="full_name" name="full_name" value={profileData.full_name} onChange={handleChange} placeholder="John Doe" />
        </div>
        <div>
          <Label htmlFor="profile_photo_url">Profile Photo</Label>
          <div className="mt-1 flex items-center gap-4">
            {profileData.profile_photo_url ? (
              <div className="relative">
                <img src={profileData.profile_photo_url} alt="Profile" className="h-16 w-16 rounded-full object-cover" />
                <Button variant="destructive" size="icon" className="absolute -top-2 -right-2 h-6 w-6" onClick={handleDeletePhoto} disabled={isDeleting}>
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
            ) : (
              <div className="h-16 w-16 rounded-full bg-muted flex items-center justify-center">
                <Upload className="h-6 w-6 text-muted-foreground" />
              </div>
            )}
            <Button variant="outline" className="relative" disabled={isUploading}>
              {isUploading ? "Uploading..." : "Upload Photo"}
              <input
                type="file"
                className="absolute inset-0 w-full h-full opacity-0 cursor-pointer disabled:cursor-not-allowed"
                onChange={handleFileUpload}
                accept="image/*"
                disabled={isUploading}
              />
            </Button>
          </div>
        </div>
        <div>
          <Label htmlFor="email">Email Address</Label>
          <Input id="email" name="email" type="email" value={profileData.email} onChange={handleChange} placeholder="<EMAIL>" />
        </div>
        <div>
          <Label htmlFor="phone_number">Phone Number</Label>
          <Input id="phone_number" name="phone_number" value={profileData.phone_number} onChange={handleChange} placeholder="+****************" />
        </div>
        <div>
          <Label htmlFor="location">Location</Label>
          <Input id="location" name="location" value={profileData.location} onChange={handleChange} placeholder="City, Country" />
        </div>
        <div>
          <Label htmlFor="timezone">Time Zone</Label>
          <Input id="timezone" name="timezone" value={profileData.timezone} onChange={handleChange} placeholder="UTC+0" />
        </div>
      </div>
    </div>
  );
};
