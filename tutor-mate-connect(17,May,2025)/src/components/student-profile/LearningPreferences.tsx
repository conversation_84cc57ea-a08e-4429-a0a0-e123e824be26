import { useStudentProfile } from "@/contexts/StudentProfileContext";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Textarea } from "@/components/ui/textarea";

export const LearningPreferences = () => {
  const { profileData, setProfileData } = useStudentProfile();

  const handleStyleChange = (value: string) => {
    setProfileData({ ...profileData, learning_style: value });
  };

  const handleGoalsChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setProfileData({ ...profileData, learning_goals: e.target.value });
  };

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h3 className="text-lg font-semibold">Learning Preferences</h3>
        <p className="text-sm text-muted-foreground">
          Help us understand how you learn best.
        </p>
      </div>

      <div className="space-y-4">
        <div>
          <Label>Preferred Learning Style</Label>
          <RadioGroup
            value={profileData.learning_style}
            onValueChange={handleStyleChange}
            className="mt-2 space-y-2"
          >
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="visual" id="visual" />
              <Label htmlFor="visual">Visual Learning</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="auditory" id="auditory" />
              <Label htmlFor="auditory">Auditory Learning</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="kinesthetic" id="kinesthetic" />
              <Label htmlFor="kinesthetic">Hands-on Learning</Label>
            </div>
          </RadioGroup>
        </div>

        <div>
          <Label htmlFor="learningGoals">Learning Goals</Label>
          <Textarea
            id="learningGoals"
            value={profileData.learning_goals}
            onChange={handleGoalsChange}
            placeholder="What do you hope to achieve with tutoring?"
            className="mt-1"
          />
        </div>
      </div>
    </div>
  );
};