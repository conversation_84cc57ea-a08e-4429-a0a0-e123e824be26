
import { useEffect, useState } from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Inbox as InboxIcon } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/components/ui/use-toast";

interface Message {
  id: string;
  content: string;
  sender_id: string;
  created_at: string;
  read: boolean;
  sender_profile: {
    full_name: string | null;
  } | null;
}

const Inbox = () => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [loading, setLoading] = useState(true);
  const { toast } = useToast();

  useEffect(() => {
    const fetchMessages = async () => {
      try {
        const { data: { user } } = await supabase.auth.getUser();
        if (!user) return;

        const { data: messagesData, error: messagesError } = await supabase
          .from('messages')
          .select('*')
          .eq('recipient_id', user.id)
          .order('created_at', { ascending: false })
          .limit(5);

        if (messagesError) throw messagesError;
        
        const messagesWithProfiles = await Promise.all(
          (messagesData || []).map(async (message) => {
            const { data: profileData } = await supabase
              .from('profiles')
              .select('full_name')
              .eq('id', message.sender_id)
              .single();

            return {
              ...message,
              sender_profile: profileData || { full_name: null }
            };
          })
        );

        setMessages(messagesWithProfiles);
      } catch (error) {
        console.error('Error fetching messages:', error);
        toast({
          title: "Error",
          description: "Failed to load messages",
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };

    fetchMessages();
  }, [toast]);



  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
    });
  };

  return (
    <Card className="p-6">
      <div className="mb-4 flex items-center justify-between">
        <h2 className="text-xl font-semibold text-sage-800">Inbox</h2>
        <Button 
          variant="outline" 
          className="text-sage-600"
          onClick={() => window.location.href = '/messages'}
        >
          View All
        </Button>
      </div>

      <div className="space-y-4">
        {loading ? (
          <p className="text-sage-600">Loading messages...</p>
        ) : messages.length === 0 ? (
          <div className="flex flex-col items-center justify-center py-6 text-sage-600">
            <InboxIcon className="mb-2 h-12 w-12" />
            <p>No messages yet</p>
          </div>
        ) : (
          messages.map((message) => (
            <div
              key={message.id}
              className={`flex items-center justify-between rounded-lg bg-white p-4 shadow-sm ${
                !message.read ? 'border-l-4 border-sage-600' : ''
              }`}
            >
              <div>
                <p className="font-medium text-sage-800">
                  {message.sender_profile?.full_name || 'Unknown Sender'}
                </p>
                <p className="text-sm text-sage-600">{message.content}</p>
              </div>
              <span className="text-sm text-sage-500">
                {formatDate(message.created_at)}
              </span>
            </div>
          ))
        )}
      </div>
    </Card>
  );
};

export default Inbox;