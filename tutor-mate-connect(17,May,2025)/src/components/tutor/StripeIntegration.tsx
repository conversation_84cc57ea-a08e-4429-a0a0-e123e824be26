import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { useToast } from "@/components/ui/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { CreditCard } from "lucide-react";

export const StripeIntegration = () => {
  const [stripeConnected, setStripeConnected] = useState<boolean | null>(null);
  const [tutorId, setTutorId] = useState<string | null>(null);
  const [userEmail, setUserEmail] = useState<string | null>(null);
  const [connectingStripe, setConnectingStripe] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    const fetchStripeStatus = async () => {
      try {
        const { data: { user } } = await supabase.auth.getUser();
        if (!user) return;

        setUserEmail(user.email);

        const { data: tutorProfile } = await supabase
          .from('tutor_profiles')
          .select('id, stripe_account_id')
          .eq('email', user.email)
          .single();

        if (tutorProfile) {
          setTutorId(tutorProfile.id);
          setStripeConnected(!!tutorProfile.stripe_account_id);
        }
      } catch (error) {
        console.error('Error fetching Stripe status:', error);
        toast({
          title: "Error",
          description: "Failed to load Stripe connection status",
          variant: "destructive",
        });
      }
    };

    fetchStripeStatus();
  }, [toast]);

  const handleConnectStripe = async () => {
    if (!tutorId || !userEmail) {
      toast({
        title: "Error",
        description: "Tutor profile information not available",
        variant: "destructive",
      });
      return;
    }

    setConnectingStripe(true);
    try {
      const SUPABASE_FUNCTIONS_URL = `${import.meta.env.VITE_SUPABASE_URL}/functions/v1`;
      
      const response = await fetch(
        `${SUPABASE_FUNCTIONS_URL}/stripe-onboard`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${import.meta.env.VITE_SUPABASE_ANON_KEY}`,
          },
          body: JSON.stringify({
            tutor_id: tutorId,
            email: userEmail
          }),
        }
      );

      const data = await response.json();
      
      if (data.url) {
        window.location.href = data.url;
      } else {
        throw new Error(data.error || "Could not get onboarding URL");
      }
    } catch (error) {
      console.error("Stripe onboarding error:", error);
      toast({
        title: "Stripe Connection Error",
        description: "Could not start Stripe onboarding.",
        variant: "destructive",
      });
    } finally {
      setConnectingStripe(false);
    }
  };

  // If Stripe is already connected, show connected status
  if (stripeConnected === true) {
    return (
      <Card className="p-6">
        <div className="flex items-center gap-3">
          <div className="rounded-full bg-green-100 p-3">
            <CreditCard className="h-6 w-6 text-green-600" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-sage-800">Stripe Connected</h3>
            <p className="text-sm text-sage-600">
              Your payment processing is set up and ready to receive payments.
            </p>
          </div>
        </div>
      </Card>
    );
  }

  // If Stripe is not connected, show connection button
  if (stripeConnected === false) {
    return (
      <Card className="p-6">
        <div className="flex items-center gap-3 mb-4">
          <div className="rounded-full bg-sage-100 p-3">
            <CreditCard className="h-6 w-6 text-sage-600" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-sage-800">Payment Setup</h3>
            <p className="text-sm text-sage-600">
              Connect your Stripe account to receive payments from students.
            </p>
          </div>
        </div>
        <Button
          variant="default"
          className="w-full"
          onClick={handleConnectStripe}
          disabled={connectingStripe}
        >
          {connectingStripe ? "Connecting..." : "Connect your Stripe account"}
        </Button>
      </Card>
    );
  }

  // Loading state
  return (
    <Card className="p-6">
      <div className="flex items-center gap-3">
        <div className="rounded-full bg-sage-100 p-3">
          <CreditCard className="h-6 w-6 text-sage-600" />
        </div>
        <div>
          <h3 className="text-lg font-semibold text-sage-800">Payment Setup</h3>
          <p className="text-sm text-sage-600">Loading payment status...</p>
        </div>
      </div>
    </Card>
  );
};
