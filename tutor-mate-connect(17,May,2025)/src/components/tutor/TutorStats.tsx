import { Card } from "@/components/ui/card";
import { useNavigate } from "react-router-dom";
import {
  Users,
  Calendar,
  Clock,
  TrendingUp,
} from "lucide-react";

const TutorStats = () => {
  const navigate = useNavigate();

  const stats = [
    {
      title: "Active Students",
      value: "12",
      icon: Users,
      path: "/active-students",
    },
    {
      title: "Hours Taught",
      value: "156",
      icon: Clock,
    },
    {
      title: "Upcoming Sessions",
      value: "8",
      icon: Calendar,
    },
    {
      title: "Earnings This Month",
      value: "$2,450",
      icon: TrendingUp,
      path: "/tutor/earnings",
    },
  ];

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      {stats.map((stat, index) => (
        <Card
          key={index}
          className={`p-6 ${
            stat.path
              ? "cursor-pointer transition-colors hover:bg-sage-50"
              : ""
          }`}
          onClick={() => stat.path && navigate(stat.path)}
        >
          <div className="flex items-center gap-4">
            <div className="rounded-full bg-sage-100 p-3">
              <stat.icon className="h-6 w-6 text-sage-600" />
            </div>
            <div>
              <p className="text-sm text-sage-600">{stat.title}</p>
              <h4 className="text-2xl font-semibold text-sage-800">
                {stat.value}
              </h4>
            </div>
          </div>
        </Card>
      ))}
    </div>
  );
};

export default TutorStats;