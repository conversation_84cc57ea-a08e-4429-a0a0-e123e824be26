import { useEffect, useState } from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { supabase } from "@/integrations/supabase/client";
import { Calendar } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { useToast } from "@/components/ui/use-toast";

const TutorSchedule = () => {
  const [sessions, setSessions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [viewType, setViewType] = useState("upcoming");
  const [currentPage, setCurrentPage] = useState(1);
  const [cancelSession, setCancelSession] = useState(null);
  const [isCancelModalOpen, setIsCancelModalOpen] = useState(false);
  const { toast } = useToast();
  const sessionsPerPage = 4;
  const navigate = useNavigate();

  useEffect(() => {
    const fetchSessions = async () => {
      setLoading(true);
      const { data: userData, error: userError } = await supabase.auth.getUser();
  
      if (userError || !userData?.user) {
        console.error("Error getting user:", userError);
        setLoading(false);
        return;
      }
  
      const tutorId = userData.user.id;
      
      console.log(tutorId)
      const { data, error } = await supabase
        .from("tutor_sessions")
        .select("*")
        .eq("tutor_id", tutorId)  
        .order("start_time", { ascending: true });
      if (error) {
        console.error("Error fetching sessions:", error);
      } else {
        
        const sortedData = data.sort((a, b) => {
          if (a.status === "active" && b.status !== "active") return -1;
          if (a.status !== "active" && b.status === "active") return 1;
          return new Date(a.start_time).getTime() - new Date(b.start_time).getTime();
        });
        setSessions(sortedData);
      }
      setLoading(false);
    };
  
    fetchSessions();
  }, []);
  

  
  const filteredSessions = () => {
    if (viewType === "all") return sessions;
    if (viewType === "upcoming") {
      return sessions.filter(
        (s) => s.status !== "canceled" && new Date(s.end_time) > new Date()
      );
    }
    if (viewType === "canceled") {
      return sessions.filter((s) => s.status === "canceled");
    }
    if (viewType === "past") {
      return sessions.filter((s) => new Date(s.end_time) < new Date());
    }
    return [];
  };

  const paginatedSessions = () => {
    const data = filteredSessions();
    const startIndex = (currentPage - 1) * sessionsPerPage;
    return data.slice(startIndex, startIndex + sessionsPerPage);
  };
  
  const totalPages = Math.ceil(filteredSessions().length / sessionsPerPage);

  const handleCancelClick = (session) => {
    setCancelSession(session);
    setIsCancelModalOpen(true);
  };


  const confirmCancel = async () => {
    if (!cancelSession) return;
  
    
    const { error: tutorError } = await supabase
      .from("tutor_sessions")
      .update({ status: "canceled" })
      .eq("id", cancelSession.id);
  
    if (tutorError) {
      toast({
        title: "Error",
        description: "Failed to cancel session for tutor.",
        variant: "destructive",
      });
      return;
    }
  
    
    const { error: studentError } = await supabase
      .from("student_sessions")
      .update({ status: "canceled" })
      .eq("event_id", cancelSession.id);
      
    if (studentError) {
      toast({
        title: "Warning",
        description: "Tutor session canceled, but failed to update student session.",
        variant: "destructive",
      });
    } else {
      toast({
        title: "Success",
        description: "Session cancelled for both tutor and student.",
      });
  
      setSessions((prev) =>
        prev.map((s) =>
          s.id === cancelSession.id ? { ...s, status: "canceled" } : s
        )
      );
    }
  
    setIsCancelModalOpen(false);
  };
  
  return (
    <Card className="p-6">
      <h1 className="text-2xl font-bold text-sage-800 mb-4 w-full">Scheduled Sessions</h1>
      <div className="mb-6">
        {viewType !== "all" && (
          <div className="flex flex-wrap gap-4 justify-center md:justify-start mb-6">
          <Button
            variant={viewType === "upcoming" ? "secondary" : "outline"}
            className="transition-all shadow hover:shadow-md rounded-xl px-5 py-2"
            onClick={() => {
              setViewType("upcoming");
              setCurrentPage(1);
            }}
          >
            Upcoming Session
          </Button>
          <Button
            variant={viewType === "canceled" ? "secondary" : "outline"}
            className="transition-all shadow hover:shadow-md rounded-xl px-5 py-2"
            onClick={() => {
              setViewType("canceled");
              setCurrentPage(1);
            }}
          >
            Canceled Session
          </Button>
          <Button
            variant={viewType === "past" ? "secondary" : "outline"}
            className="transition-all shadow hover:shadow-md rounded-xl px-5 py-2"
            onClick={() => {
              setViewType("past");
              setCurrentPage(1);
            }}
          >
            Past Session
          </Button>
          <Button
            variant={viewType === "all" ? "secondary" : "outline"}
            className="transition-all shadow hover:shadow-md rounded-xl px-5 py-2"
            onClick={() => {
              setViewType("all");
              setCurrentPage(1);
            }}
          >
            View All
          </Button>
        </div>
        
        )}

        {viewType === "all" && (
          <div className="flex justify-evenly items-center gap-6 mb-6 flex-wrap">
            <Button
              variant="outline"
              className="transition-all shadow hover:shadow-md rounded-xl px-6 py-3 min-w-[160px]"
              onClick={() => {
                setViewType("upcoming");
                setCurrentPage(1);
              }}
            >
              Upcoming Sessions
            </Button> 
            <Button
              variant="outline"
              className="transition-all shadow hover:shadow-md rounded-xl px-6 py-3 min-w-[160px]"
              onClick={() => {
                setViewType("canceled");
                setCurrentPage(1);
              }}
            >
              Canceled Sessions
            </Button>
            <Button
              variant="outline"
              className="transition-all shadow hover:shadow-md rounded-xl px-6 py-3 min-w-[160px]"
              onClick={() => {
                setViewType("past");
                setCurrentPage(1);
              }}
            >
              Past Sessions
            </Button>
          </div>
        )}
      </div>

      {loading ? (
        <p className="text-center text-sage-600">Loading...</p>
      ) : paginatedSessions().length === 0 ? (
        <div className="flex flex-col items-center justify-center py-6 text-sage-600">
          <Calendar className="mb-2 h-12 w-12" />
          <p>No {viewType} sessions found</p>
        </div>
      ) : (
        <div className="space-y-4">
          {paginatedSessions().map((session) => (
            <div
              key={session.id}
              className="flex items-center justify-between rounded-lg bg-white p-4 shadow-sm border border-sage-200"
            >
              <div>
                <p className="font-medium text-sage-800 text-lg mb-1">
                  {session.event_name}
                </p>
                <p className="text-sm text-sage-600 mb-0.5">
                  Tutor: {session.tutor_name}
                </p>
                <p className="text-sm text-sage-500">
                  {new Date(session.start_time).toLocaleDateString(undefined, {
                    year: "numeric",
                    month: "short",
                    day: "numeric",
                  })}
                </p>
                <p className="text-sm text-sage-500">
                  {new Date(session.start_time).toLocaleTimeString([], {
                    hour: "2-digit",
                    minute: "2-digit",
                  })} - {new Date(session.end_time).toLocaleTimeString([], {
                    hour: "2-digit",
                    minute: "2-digit",
                  })}
                </p>
                <p
                  className={`text-sm mt-1 font-semibold ${
                    session.status === "canceled"
                      ? "text-red-500"
                      : session.status === "completed"
                      ? "text-gray-500"
                      : "text-green-600"
                  }`}
                >
                  Status: {session.status.charAt(0).toUpperCase() + session.status.slice(1)}
                </p>
              </div>

              {session.status === "active" && viewType === "upcoming" && (
                <div className="flex gap-2">
                  <Button
                    variant="destructive"
                    size="sm"
                    onClick={() => handleCancelClick(session)}
                  >
                    Cancel
                  </Button>
                  {session.join_url ? (
                    <a
                      href={session.join_url}
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      <Button variant="outline" size="sm" className="text-sage-600">
                        Join
                      </Button>
                    </a>
                  ) : (
                    <Button variant="secondary" size="sm" disabled>
                      No Link
                    </Button>
                  )}
                </div>
              )}
            </div>
          ))}

          <div className="flex justify-between mt-4">
            <Button
              disabled={currentPage === 1}
              onClick={() => setCurrentPage((prev) => prev - 1)}
            >
              Prev
            </Button>
            <Button
              disabled={currentPage === totalPages || totalPages === 0}
              onClick={() => setCurrentPage((prev) => prev + 1)}
            >
              Next
            </Button>
          </div>
        </div>
      )}

      <Dialog open={isCancelModalOpen} onOpenChange={setIsCancelModalOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Cancel Session</DialogTitle>
          </DialogHeader>
          <p>Are you sure you want to cancel the session "{cancelSession?.event_name}"?</p>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsCancelModalOpen(false)}>
              Close
            </Button>
            <Button variant="destructive" onClick={confirmCancel}>
              Confirm Cancel
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Card>
  );
};

export default TutorSchedule;
