import { Card } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";

const StudentProgress = () => {
  return (
    <Card className="p-6">
      <h2 className="mb-4 text-xl font-semibold text-sage-800">Student Progress</h2>
      <div className="space-y-4">
        {[1, 2, 3].map((student) => (
          <div
            key={student}
            className="flex items-center justify-between rounded-lg bg-white p-4 shadow-sm"
          >
            <div className="flex items-center gap-4">
              <div className="h-10 w-10 rounded-full bg-sage-200" />
              <div>
                <p className="font-medium text-sage-800"><PERSON></p>
                <p className="text-sm text-sage-600">Mathematics - Advanced</p>
              </div>
            </div>
            <Button variant="outline" className="text-sage-600">
              View Progress
            </Button>
          </div>
        ))}
      </div>
    </Card>
  );
};

export default StudentProgress;