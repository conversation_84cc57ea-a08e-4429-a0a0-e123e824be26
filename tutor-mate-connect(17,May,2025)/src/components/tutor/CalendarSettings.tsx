import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card } from "@/components/ui/card";
import { useToast } from "@/components/ui/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { ExternalLink } from "lucide-react";
import axios from "axios";

export const CalendarSettings = ({ scheduledEvents, setScheduledEvents }) => {
  const [calendlyUsername, setCalendlyUsername] = useState("");
  const [savedUsername, setSavedUsername] = useState(""); 
  const [isLoading, setIsLoading] = useState(false);
  const [isCalendlyConnected, setIsCalendlyConnected] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    checkAndFetchCalendlyEvents();
  }, []);

  const checkAndFetchCalendlyEvents = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;

      const { data, error } = await supabase
        .from("tutor_profiles")
        .select("calendly_username")
        .eq("id", user.id)
        .single();

      if (error) throw error;

      if (data?.calendly_username) {
        setCalendlyUsername(data.calendly_username);
        setSavedUsername(data.calendly_username);
        setIsCalendlyConnected(true);
        await fetchScheduledEvents(data.calendly_username);
      }
    } catch (error) {
      console.error("Error checking Calendly username:", error);
      toast({
        title: "Error",
        description: "Failed to load calendar settings",
        variant: "destructive",
      });
    }
  };

  const fetchScheduledEvents = async (username) => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error("User not authenticated");

      const { data: tutorData, error: tutorError } = await supabase
        .from("tutor_profiles")
        .select("id")
        .eq("id", user.id)
        .single();
    
      if (tutorError || !tutorData?.id) throw new Error("Tutor profile not found");
    
      const tutor_id = tutorData.id;
      localStorage.setItem('tutor_id', tutor_id);
      
      const apiKey = import.meta.env.VITE_CALENDLY_API;
      const userResponse = await axios.get("https://api.calendly.com/users/me", {
        headers: {
          Authorization: `Bearer ${apiKey}`,
          "Content-Type": "application/json",
        },
      });

      const userUri = userResponse.data.resource.uri;
      
      let events = [];
      let nextPageToken = null;

      do {
        const fromDate = new Date().toISOString();
        const toDate = new Date(Date.now() + 10 * 24 * 60 * 60 * 1000).toISOString();        

        const url = `https://api.calendly.com/scheduled_events?user=${encodeURIComponent(
          userUri
        )}${nextPageToken ? `&page_token=${nextPageToken}` : ""}&min_start_time=${encodeURIComponent(
          fromDate
        )}&max_start_time=${encodeURIComponent(toDate)}`;

        const response = await axios.get(url, {
          headers: {
            Authorization: `Bearer ${apiKey}`,
            "Content-Type": "application/json",
          },
        });

        const fetchedEvents = await Promise.all(
          response.data.collection.map(async (event) => {
            const eventId = event.uri.split("/").pop();
            
            const inviteesResponse = await axios.get(
              `https://api.calendly.com/scheduled_events/${eventId}/invitees`,
              {
                headers: {
                  Authorization: `Bearer ${apiKey}`,
                  "Content-Type": "application/json",
                },
              }
            );
            const invitee = inviteesResponse.data.collection[0];
            const cancelUrl = invitee?.cancel_url || null;

            const eventData = {
              id: eventId,
              created_at: new Date().toISOString(),
              start_time: event.start_time,
              end_time: event.end_time,
              event_name: event.name,
              join_url: event.location?.join_url || null,
              cancel_url: cancelUrl,
              status: event.status,
              tutor_id: tutor_id,
            };
            
            await supabase.from("tutor_sessions").upsert(eventData, { onConflict: "id" });

            return eventData;
          })
        );

        events = [...events, ...fetchedEvents];
        nextPageToken = response.data.pagination.next_page_token;
      } while (nextPageToken);

      setScheduledEvents(events);
      toast({
        title: "Success",
        description: "Scheduled events synced and stored in the database.",
      });
    } catch (error) {
      console.error("Error fetching and storing scheduled events:", error);
      toast({
        title: "Error",
        description: "Failed to fetch and store events.",
        variant: "destructive",
      });
    }
  };

  const verifyCalendlyUsername = async (username) => {
    try {
      const { data: { session } } = await supabase.auth.getSession();
      if (!session?.access_token) return { isValid: false, message: "User not authenticated." };

      const response = await fetch(
        "https://hhynjhfcsqsbynfdbpjg.supabase.co/functions/v1/my-function",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${session.access_token}`,
          },
          body: JSON.stringify({ action: "verifyUsername", username }),
        }
      );

      const data = await response.json();
      if (!data?.exists) return { isValid: false, message: "This Calendly username does not exist." };

      return { isValid: true };
    } catch (error) {
      return { isValid: false, message: "Error verifying Calendly username." };
    }
  };

  const handleSave = async () => {
    if (!calendlyUsername.trim()) {
      toast({
        title: "Error",
        description: "Please enter a Calendly username",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsLoading(true);
      const verification = await verifyCalendlyUsername(calendlyUsername.trim());

      if (!verification.isValid) {
        toast({
          title: "Invalid Username",
          description: verification.message,
          variant: "destructive",
        });
        return;
      }

      const { data: { user }, error: userError } = await supabase.auth.getUser();
      if (userError || !user) throw new Error("User authentication failed.");

      const trimmedUsername = calendlyUsername.trim(); 

      const { error: updateError } = await supabase
        .from("tutor_profiles")
        .update({ calendly_username: trimmedUsername } )
        .eq("id", user.id);

      if (updateError) throw new Error("Failed to save Calendly username.");

      await fetchScheduledEvents(trimmedUsername);
      setSavedUsername(trimmedUsername);
      setIsCalendlyConnected(true);

      toast({
        title: "Success",
        description: "Calendly username saved and events synced.",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: error.message || "Failed to save Calendly username.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // If Calendly is already connected, show events
  if (isCalendlyConnected) {
    return (
      <Card className="p-6">
        <div className="text-sage-700">
          <p>
            Your Calendly is connected as <strong>{calendlyUsername}</strong>.
          </p>
        </div>
      </Card>
    );
  }

  // Show form to connect Calendly
  return (
    <Card className="p-6">
      <div className="space-y-6">
        <div>
          <h3 className="text-lg font-semibold text-sage-800">Calendar Integration</h3>
          <p className="text-sm text-sage-600">
            Connect your Calendly calendar to manage your availability and bookings.
          </p>
        </div>

        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="calendly_username">Calendly Username</Label>
            <Input
              id="calendly_username"
              value={calendlyUsername}
              onChange={(e) => setCalendlyUsername(e.target.value)}
              placeholder="your-username"
            />
            <div className="flex items-center gap-2 text-sm text-sage-500">
              <p>Don't have a Calendly account?</p>
              <a
                href="https://calendly.com/signup"
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center gap-1 text-sage-600 hover:text-sage-700 hover:underline"
              >
                Sign up here
                <ExternalLink className="h-3 w-3" />
              </a>
            </div>
          </div>

          <Button onClick={handleSave} disabled={isLoading}>
            {isLoading ? "Saving..." : "Save Settings"}
          </Button>
        </div>
      </div>
    </Card>
  );
};


