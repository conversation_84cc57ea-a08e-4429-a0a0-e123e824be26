import { <PERSON> } from "react-router-dom";
import { Button } from "@/components/ui/button";
import ProfileMenu from "./ProfileMenu";

interface MobileMenuProps {
  isOpen: boolean;
  menuItems: Array<{ name: string; path: string }>;
  user: any;
  onClose: () => void;
  onLogin: () => void;
}

const MobileMenu = ({ isOpen, menuItems, user, onClose, onLogin }: MobileMenuProps) => {
  return (
    <div className={`${isOpen ? "block" : "hidden"} md:hidden`}>
      <div className="px-2 pt-2 pb-3 space-y-1">
        {menuItems.map((item) => (
          <Link
            key={item.path}
            to={item.path}
            className="block px-3 py-2 rounded-md text-base font-medium text-gray-600 hover:bg-sage-50 hover:text-sage-600"
            onClick={onClose}
          >
            {item.name}
          </Link>
        ))}
        <div className="mt-4 px-3">
        {user ? (
  <div className="space-y-2">
    <ProfileMenu setIsMenuOpen={onClose} />
  </div>
) : (
  <Button onClick={onLogin} variant="outline" className="w-full">
    Login
  </Button>
)}

        </div>
      </div>
    </div>
  );
};

export default MobileMenu;