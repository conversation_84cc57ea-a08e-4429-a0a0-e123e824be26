import { User, MessageSquare, LogOut, LayoutDashboard } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/components/ui/use-toast";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import { useState, useEffect } from "react";
import { clear } from "console";

interface ProfileMenuProps {
  setIsMenuOpen?: (isOpen: boolean) => void;
}

const ProfileMenu = ({ setIsMenuOpen }: ProfileMenuProps) => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const user = localStorage.getItem('userRole')
  const [userRole, setUserRole] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [profilePhotoUrl, setProfilePhotoUrl] = useState<string | null>(null);

  useEffect(() => {
    const fetchUserProfile = async () => {
      try {
        const { data: { user }, error: authError } = await supabase.auth.getUser();
        if (authError || !user) {
          throw new Error("Authentication error: User not found.");
        }
  
        const userId = user.id;
  
        
        const [{ data: tutorData, error: tutorError }, { data: studentData, error: studentError }, { data: profileData, error: profileError }] = await Promise.all([
          supabase.from('tutor_profiles').select('profile_photo_url').eq('id', userId).maybeSingle(),
          supabase.from('student_profiles').select('profile_photo_url').eq('id', userId).maybeSingle(),
          supabase.from('profiles').select('role').eq('id', userId).maybeSingle(),
        ]);
  
        
  
        
        if (tutorError || studentError || profileError) {
          throw new Error("Error querying profile tables.");
        }
  
        
        if (tutorData?.profile_photo_url) {
          setProfilePhotoUrl(tutorData.profile_photo_url);
          setUserRole('tutor');
        } else if (studentData?.profile_photo_url) {
          setProfilePhotoUrl(studentData.profile_photo_url);
          setUserRole('student');
        } else if (profileData?.role) {
          setUserRole(profileData.role);
        } else {
          
          setUserRole(null); 
        }
  
      } catch (error) {
        console.error("Error fetching user profile:", error.message);
        toast({
          title: "Error",
          description: "Failed to load user profile",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };
  
    fetchUserProfile();
  }, [toast]);
  
  
  
 

  const handleLogout = async () => {
    
    try {
      const { error } = await supabase.auth.signOut();
      if (error) throw error;
      localStorage.clear();
      if (setIsMenuOpen) {
        setIsMenuOpen(false);
      }
      console.clear();
      navigate("/");
      
      toast({
        title: "Logged out successfully",
        duration: 3000,
      });
    } catch (error) {
      console.error('Error logging out:', error);
      toast({
        title: "Error logging out",
        description: "Please try again",
        variant: "destructive",
      });
    }
  };
 
  const handleDashboardClick = () => {
    const dashboardPath = user === 'tutor' ? '/tutor-dashboard' : '/student-dashboard';
    
    navigate(dashboardPath);
    if (setIsMenuOpen) {
      setIsMenuOpen(false);
    }
  };

  if (isLoading) {
    return (
      <div className="h-8 w-8 animate-pulse bg-gray-200 rounded-full"></div>
    );
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger className="flex items-center gap-2 p-2 rounded-full hover:bg-sage-50">
        <Avatar className="h-8 w-8">
          {profilePhotoUrl ? (
            <AvatarImage 
              src={profilePhotoUrl} 
              alt="Profile" 
              className="object-cover"
            />
          ) : (
            <AvatarFallback>
              <User className="h-4 w-4 text-sage-600" />
            </AvatarFallback>
          )}
        </Avatar>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-48">
        <DropdownMenuItem onClick={handleDashboardClick}>
          <LayoutDashboard className="mr-2 h-4 w-4" />
          My Dashboard
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => navigate("/profile")}>
          <User className="mr-2 h-4 w-4" />
          My Profile
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => navigate("/contact")}>
          <MessageSquare className="mr-2 h-4 w-4" />
          Contact Us
        </DropdownMenuItem>
        <DropdownMenuItem onClick={handleLogout}>
          <LogOut className="mr-2 h-4 w-4" />
          Log Out
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default ProfileMenu;