import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Clock } from "lucide-react";

interface TutorCardProps {
  name: string;
  subject?: string;
  rating: number;
  hourlyRate: number;
  imageUrl: string;
  specialties: string[];
  bio?: string;
  onBookSession?: () => void;
}

export const TutorCard = ({
  name,
  rating,
  hourlyRate,
  imageUrl,
  specialties,
  bio,
  onBookSession,
}: TutorCardProps) => {
  return (
    <Card className="group overflow-hidden transition-all duration-300 hover:shadow-lg">
      <div className="relative aspect-[4/3] overflow-hidden">
        <img
          src={imageUrl}
          alt={name}
          className="h-full w-full object-cover transition-transform duration-300 group-hover:scale-105"
          style={{ objectPosition: "center 35%" }}
          loading="lazy"
        />
        <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
      </div>
      <div className="p-6">
        <div className="mb-4">
          <h3 className="text-2xl font-semibold tracking-tight">{name}</h3>
          {bio && (
            <p className="mt-2 text-sm text-muted-foreground line-clamp-2">{bio}</p>
          )}
        </div>
        <div className="mb-4 flex flex-wrap gap-2">
          {specialties.map((specialty) => (
            <Badge key={specialty} variant="outline" className="bg-sage-50">
              {specialty}
            </Badge>
          ))}
        </div>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Clock className="h-4 w-4 text-muted-foreground" />
            <span className="font-medium">${hourlyRate}/hr</span>
          </div>
          <Button 
            variant="default" 
            className="bg-sage-600 hover:bg-sage-700"
            onClick={onBookSession}
          >
            Book Session
          </Button>
        </div>
      </div>
    </Card>
  );
};