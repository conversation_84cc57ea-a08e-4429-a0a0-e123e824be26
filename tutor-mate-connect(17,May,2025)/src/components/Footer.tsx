import { Mail, Facebook, Twitter, Instagram, Linkedin, GraduationCap, BookOpen, Users, Info } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useToast } from "@/components/ui/use-toast";
import { Link, useNavigate } from "react-router-dom";

export const Footer = () => {
  const { toast } = useToast();
  const navigate = useNavigate();
  const user = localStorage.getItem("user");

  const handleNewsletterSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const formData = new FormData(e.currentTarget);
    const email = formData.get('email');
    
    
    
    toast({
      title: "Success!",
      description: "Thank you for signing up for our newsletter!",
      duration: 3000,
    });
    
    (e.target as HTMLFormElement).reset();
  };

  const handleProtectedRoute = (path: string) => {
    if (!user) {
      navigate('/login');
      toast({
        title: "Authentication Required",
        description: "Please log in to access this feature",
        duration: 3000,
      });
    } else {
      navigate(path);
    }
  };

  return (
    <footer className="bg-sage-800 text-white mt-20">
      <div className="max-w-7xl mx-auto px-4 py-12 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8">
          {/* About Us Section */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <Info className="h-5 w-5" />
              About Us
            </h3>
            <ul className="space-y-2 text-sage-200">
              <li><Link to="/about" className="hover:text-white transition-colors">Our Story</Link></li>
              <li><Link to="/contact" className="hover:text-white transition-colors">Contact Us</Link></li>
              <li><Link to="/about/careers" className="hover:text-white transition-colors">Careers</Link></li>
              <li><Link to="/about/press" className="hover:text-white transition-colors">Press</Link></li>
            </ul>
          </div>

          {/* For Students Section */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <GraduationCap className="h-5 w-5" />
              For Students
            </h3>
            <ul className="space-y-2 text-sage-200">
              <li><Link to="/find-tutor" className="hover:text-white transition-colors">Find a Tutor</Link></li>
              <li><Link to="/subjects" className="hover:text-white transition-colors">Browse Subjects</Link></li>
              <li><Link to="/resources" className="hover:text-white transition-colors">Learning Resources</Link></li>
              <li>
                <button 
                  onClick={() => handleProtectedRoute('/student-dashboard')} 
                  className="hover:text-white transition-colors"
                >
                  Student Dashboard
                </button>
              </li>
            </ul>
          </div>

          {/* For Tutors Section */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <BookOpen className="h-5 w-5" />
              For Tutors
            </h3>
            <ul className="space-y-2 text-sage-200">
              <li><Link to="/become-tutor" className="hover:text-white transition-colors">Become a Tutor</Link></li>
              <li>
                <button 
                  onClick={() => handleProtectedRoute('/tutor-dashboard')} 
                  className="hover:text-white transition-colors"
                >
                  Tutor Dashboard
                </button>
              </li>
              <li><Link to="/resources#teaching" className="hover:text-white transition-colors">Teaching Resources</Link></li>
              <li>
                <button 
                  onClick={() => handleProtectedRoute('/tutor/earnings')} 
                  className="hover:text-white transition-colors"
                >
                  Earnings
                </button>
              </li>
            </ul>
          </div>

          {/* Social Links */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <Users className="h-5 w-5" />
              Connect With Us
            </h3>
            <div className="flex space-x-4">
              <a href="https://facebook.com" target="_blank" rel="noopener noreferrer" className="hover:text-sage-300 transition-colors">
                <Facebook className="h-6 w-6" />
              </a>
              <a href="https://twitter.com" target="_blank" rel="noopener noreferrer" className="hover:text-sage-300 transition-colors">
                <Twitter className="h-6 w-6" />
              </a>
              <a href="https://instagram.com" target="_blank" rel="noopener noreferrer" className="hover:text-sage-300 transition-colors">
                <Instagram className="h-6 w-6" />
              </a>
              <a href="https://linkedin.com" target="_blank" rel="noopener noreferrer" className="hover:text-sage-300 transition-colors">
                <Linkedin className="h-6 w-6" />
              </a>
            </div>
          </div>

          {/* Newsletter Signup */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <Mail className="h-5 w-5" />
              Stay Updated
            </h3>
            <form onSubmit={handleNewsletterSubmit} className="space-y-2">
              <p className="text-sm text-sage-200">Sign up for our newsletter to know when we launch!</p>
              <div className="flex flex-col space-y-2">
                <Input
                  type="email"
                  name="email"
                  placeholder="Enter your email"
                  required
                  className="bg-sage-700 border-sage-600 text-white placeholder:text-sage-400"
                />
                <Button type="submit" variant="secondary" className="w-full">
                  Subscribe
                </Button>
              </div>
            </form>
          </div>

        </div>

        {/* Bottom Section */}
        <div className="mt-12 pt-8 border-t border-sage-700">
          <div className="text-center text-sage-300 text-sm">
            <p>&copy; {new Date().getFullYear()} Prime Tutors. All rights reserved.</p>
            <div className="mt-2 space-x-4">
              <Link to="/privacy" className="hover:text-white transition-colors">Privacy Policy</Link>
              <span>•</span>
              <Link to="/terms" className="hover:text-white transition-colors">Terms of Service</Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};
