import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";

type StudentParentInfoProps = {
  profileData: any;
  isEditing: boolean;
  handleChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => void;
};

export const StudentParentInfo = ({
  profileData,
  isEditing,
  handleChange,
}: StudentParentInfoProps) => {
  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold">Parent/Guardian Information</h3>
      <div className="grid gap-4">
        <div className="space-y-2">
          <Label htmlFor="parent_info_name">Name</Label>
          <Input
            id="parent_info_name"
            name="parent_info_name"
            value={profileData.parent_info?.name || ''}
            onChange={handleChange}
            readOnly={!isEditing}
            className={!isEditing ? "bg-gray-100" : ""}
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="parent_info_email">Email</Label>
          <Input
            id="parent_info_email"
            name="parent_info_email"
            type="email"
            value={profileData.parent_info?.email || ''}
            onChange={handleChange}
            readOnly={!isEditing}
            className={!isEditing ? "bg-gray-100" : ""}
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="parent_info_phone">Phone</Label>
          <Input
            id="parent_info_phone"
            name="parent_info_phone"
            value={profileData.parent_info?.phone || ''}
            onChange={handleChange}
            readOnly={!isEditing}
            className={!isEditing ? "bg-gray-100" : ""}
          />
        </div>
      </div>
    </div>
  );
};