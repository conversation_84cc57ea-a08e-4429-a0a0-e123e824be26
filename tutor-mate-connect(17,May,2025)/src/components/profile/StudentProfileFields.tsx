import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { X } from "lucide-react";
import React, { useState, useEffect } from "react";

type StudentProfileFieldsProps = {
  profileData: any;
  handleChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => void;
  handleSelectChange: (field: string, value: string | string[]) => void;
  isEditing: boolean;
};

export const StudentProfileFields = ({
  profileData,
  handleChange,
  handleSelectChange,
  isEditing,
}: StudentProfileFieldsProps) => {
  const [inputValue, setInputValue] = useState('');
  const [languageValue, setLanguageValue] = useState('');

  useEffect(() => {
    setInputValue('');
    setLanguageValue('');
  }, [isEditing, profileData]);

  const subjects: string[] = Array.isArray(profileData?.subjects)
    ? profileData.subjects
    : (profileData?.subjects?JSON.parse(profileData?.subjects):[]);

  const preferredLanguages: string[] = Array.isArray(profileData?.preferred_languages)
    ? profileData.preferred_languages
    : [];

  const handleSubjectKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' && inputValue.trim()) {
      e.preventDefault();
      if (!subjects.includes(inputValue.trim())) {
        handleSelectChange('subjects', [...subjects, inputValue.trim()]);
      }
      setInputValue('');
    }
  };

  const handleLanguageKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' && languageValue.trim()) {
      e.preventDefault();
      if (!preferredLanguages.includes(languageValue.trim())) {
        handleSelectChange('preferred_languages', [...preferredLanguages, languageValue.trim()]);
      }
      setLanguageValue('');
    }
  };

  const removeSubject = (subject: string) => {
    handleSelectChange(
      'subjects',
      subjects.filter((s) => s !== subject)
    );
  };

  const removeLanguage = (language: string) => {
    handleSelectChange(
      'preferred_languages',
      preferredLanguages.filter((l) => l !== language)
    );
  };

  return (
    <div className="space-y-6">
      <div className="space-y-4">

        
        <div className="space-y-2">
          <Label htmlFor="grade_level">Grade Level</Label>
          <Select
            value={profileData?.grade_level || ''}
            onValueChange={(value) => handleSelectChange('grade_level', value)}
            disabled={!isEditing}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select your grade level" />
            </SelectTrigger>
            <SelectContent>
              {[
                "6th Grade",
                "7th Grade",
                "8th Grade",
                "9th Grade",
                "10th Grade",
                "11th Grade",
                "12th Grade",
                "College",
              ].map((grade) => (
                <SelectItem key={grade} value={grade}>
                  {grade}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        
        <div className="space-y-2">
          <Label htmlFor="subjects">Subjects</Label>
          <div className="flex flex-wrap gap-2">
            {subjects.map((subject, index) => (
              <div key={`${subject}-${index}`} className="flex items-center gap-1 px-2 py-1 bg-blue-100 rounded">
                <span>{subject}</span>
                {isEditing && (
                  <button type="button" onClick={() => removeSubject(subject)} className="text-blue-500 hover:text-blue-700">
                    <X size={14} />
                  </button>
                )}
              </div>
            ))}
          </div>
          <Input
            id="subjects"
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onKeyDown={handleSubjectKeyDown}
            placeholder="Type a subject and press Enter"
            className={!isEditing ? "bg-gray-100" : ""}
            readOnly={!isEditing}
            disabled={!isEditing}
          />
        </div>

        
        <div className="space-y-2">
          <Label htmlFor="preferred_languages">Preferred Languages</Label>
          <div className="flex flex-wrap gap-2">
            {preferredLanguages.map((language, index) => (
              <div key={`${language}-${index}`} className="flex items-center gap-1 px-2 py-1 bg-blue-100 rounded">
                <span>{language}</span>
                {isEditing && (
                  <button type="button" onClick={() => removeLanguage(language)} className="text-blue-500 hover:text-blue-700">
                    <X size={14} />
                  </button>
                )}
              </div>
            ))}
          </div>
          <Input
            id="preferred_languages"
            value={languageValue}
            onChange={(e) => setLanguageValue(e.target.value)}
            onKeyDown={handleLanguageKeyDown}
            placeholder="Type a language and press Enter"
            className={!isEditing ? "bg-gray-100" : ""}
            readOnly={!isEditing}
            disabled={!isEditing}
          />
        </div>

        
        <div className="space-y-2">
          <Label htmlFor="learning_style">Learning Style</Label>
          <Select
            value={profileData?.learning_style || ''}
            onValueChange={(value) => handleSelectChange('learning_style', value)}
            disabled={!isEditing}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select your learning style" />
            </SelectTrigger>
            <SelectContent>
              {["Visual", "Auditory", "Kinesthetic"].map((style) => (
                <SelectItem key={style} value={style.toLowerCase()}>
                  {style} Learning
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        
        <div className="space-y-2">
          <Label htmlFor="learning_goals">Learning Goals</Label>
          <Textarea
            id="learning_goals"
            name="learning_goals"
            value={profileData?.learning_goals || ''}
            onChange={handleChange}
            placeholder="What do you hope to achieve?"
            readOnly={!isEditing}
            className={!isEditing ? "bg-gray-100" : ""}
          />
        </div>

        
        <div className="space-y-2">
          <Label htmlFor="special_needs">Special Needs or Accommodations</Label>
          <Textarea
            id="special_needs"
            name="special_needs"
            value={profileData?.special_needs || ''}
            onChange={handleChange}
            placeholder="Any special needs or accommodations we should know about?"
            readOnly={!isEditing}
            className={!isEditing ? "bg-gray-100" : ""}
          />
        </div>
      </div>
    </div>
  );
};

export default StudentProfileFields;
