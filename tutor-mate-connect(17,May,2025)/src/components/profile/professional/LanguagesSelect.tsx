import { useState, useEffect } from "react";
import { Label } from "@/components/ui/label";
import { fetchLanguages, Language } from "@/data/languages";
import { ScrollArea } from "@/components/ui/scroll-area";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/components/ui/use-toast";
import { Button } from "@/components/ui/button";
import { Check } from "lucide-react";

interface LanguagesSelectProps {
  selectedLanguages: string[];
  onLanguageSelect: (value: string) => void;
  onLanguageRemove: (index: number) => void;
}

export const LanguagesSelect = ({
  selectedLanguages = [],
  onLanguageSelect,
  onLanguageRemove,
}: LanguagesSelectProps) => {
  const [languages, setLanguages] = useState<Language[]>([]);
  const [loading, setLoading] = useState(true);
  const { toast } = useToast();

  useEffect(() => {
    const loadLanguages = async () => {
      try {
        const { data: { user } } = await supabase.auth.getUser();
        if (!user) return;

        const languagesData = await fetchLanguages();
        setLanguages(languagesData);
        setLoading(false);
      } catch (error) {
        console.error('Error loading languages:', error);
        toast({
          title: "Error",
          description: "Failed to load languages",
          variant: "destructive",
        });
        setLoading(false);
      }
    };

    loadLanguages();
  }, [toast]);

  const handleLanguageToggle = async (languageId: number, languageName: string) => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;

      const isSelected = selectedLanguages.includes(languageName);

      if (!isSelected) {
        
        const { error: insertError } = await supabase
          .from('tutor_languages')
          .upsert({ 
            tutor_id: user.id, 
            language_id: languageId 
          });

        if (insertError) throw insertError;
        onLanguageSelect(languageName);
        
        toast({
          title: "Language added",
          description: `${languageName} has been added to your languages.`,
        });
      } else {
        
        const { error: deleteError } = await supabase
          .from('tutor_languages')
          .delete()
          .eq('tutor_id', user.id)
          .eq('language_id', languageId);

        if (deleteError) throw deleteError;
        const index = selectedLanguages.indexOf(languageName);
        if (index !== -1) {
          onLanguageRemove(index);
        }
        
        toast({
          title: "Language removed",
          description: `${languageName} has been removed from your languages.`,
        });
      }
    } catch (error) {
      console.error('Error updating language:', error);
      toast({
        title: "Error",
        description: "Failed to update language selection",
        variant: "destructive",
      });
    }
  };

  if (loading) {
    return <div>Loading languages...</div>;
  }

  return (
    <div className="space-y-4">
      <Label>Languages</Label>
      <ScrollArea className="h-[200px] rounded-md border p-4">
        <div className="space-y-2">
          {languages.map((language) => {
            const isSelected = selectedLanguages?.includes(language.name);
            return (
              <Button
                key={language.id}
                variant={isSelected ? "default" : "outline"}
                className="w-full justify-between transition-all duration-200 hover:bg-primary/20"
                onClick={() => handleLanguageToggle(language.id, language.name)}
              >
                <span>{language.name}</span>
                <div className={`flex h-5 w-5 items-center justify-center rounded-full border-2 transition-colors ${
                  isSelected ? 'bg-primary border-primary' : 'border-primary/50'
                }`}>
                  {isSelected && <Check className="h-3 w-3 text-primary-foreground" />}
                </div>
              </Button>
            );
          })}
        </div>
      </ScrollArea>
      <div className="mt-2">
        <p className="text-sm text-muted-foreground">
          Selected: {selectedLanguages?.length || 0} languages
        </p>
      </div>
    </div>
  );
};