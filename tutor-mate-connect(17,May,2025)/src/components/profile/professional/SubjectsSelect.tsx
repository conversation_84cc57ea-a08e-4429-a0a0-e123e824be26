import { useState, useEffect } from "react";
import { Label } from "@/components/ui/label";
import { fetchSubjects, Subject } from "@/data/subjects";
import { ScrollArea } from "@/components/ui/scroll-area";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/components/ui/use-toast";
import { Button } from "@/components/ui/button";
import { Check, X } from "lucide-react";

interface SubjectsSelectProps {
  selectedSubjects: string[];
  onSubjectSelect: (value: string) => void;
  onSubjectRemove: (index: number) => void;
}

export const SubjectsSelect = ({
  selectedSubjects = [],
  onSubjectSelect,
  onSubjectRemove,
}: SubjectsSelectProps) => {
  const [subjects, setSubjects] = useState<Subject[]>([]);
  const [loading, setLoading] = useState(true);
  const { toast } = useToast();

  useEffect(() => {
    const loadSubjects = async () => {
      try {
        const { data: { user } } = await supabase.auth.getUser();
        if (!user) return;

        const subjectsData = await fetchSubjects();
        setSubjects(subjectsData);
        setLoading(false);
      } catch (error) {
        console.error('Error loading subjects:', error);
        toast({
          title: "Error",
          description: "Failed to load subjects",
          variant: "destructive",
        });
        setLoading(false);
      }
    };

    loadSubjects();
  }, [toast]);
  const handleSubjectToggle = async (subjectId: number, subjectName: string) => {
  try {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) return;

    const isSelected = selectedSubjects.includes(subjectName);

    if (!isSelected) {
      const { error: insertError } = await supabase
        .from('tutors_subject') 
        .upsert({
          tutor_id: user.id,
          subjects_id: subjectId, 
        });
      if (insertError) throw insertError;
      onSubjectSelect(subjectName);
      toast({
        title: "Subject added",
        description: `${subjectName} has been added to your subjects.`,
      });

    } else {
      const { error: deleteError } = await supabase
        .from('tutors_subject')
        .delete()
        .eq('tutor_id', user.id)
        .eq('subjects_id', subjectId);

      if (deleteError) throw deleteError;
      const index = selectedSubjects.indexOf(subjectName);
      if (index !== -1) {
        onSubjectRemove(index);
      }

      toast({
        title: "Subject removed",
        description: `${subjectName} has been removed from your subjects.`,
      });
    }
  } catch (error) {
    console.error('Error updating subject:', error);
    toast({
      title: "Error",
      description: "Failed to update subject selection",
      variant: "destructive",
    });
  }
};


  if (loading) {
    return <div>Loading subjects...</div>;
  }

  return (
    <div className="space-y-4">
      <Label>Available Subjects</Label>
      <ScrollArea className="h-[300px] rounded-md border p-4">
        <div className="space-y-2">
          {subjects.map((subject) => {
            const isSelected = selectedSubjects.includes(subject.name);
            return (
              <Button
                key={subject.id}
                variant={isSelected ? "default" : "outline"}
                className={`w-full justify-between transition-all duration-200 ${
                  isSelected ? 'bg-primary text-primary-foreground' : 'hover:bg-primary/20'
                }`}
                onClick={() => handleSubjectToggle(subject.id, subject.name)}
              >
                <span>{subject.name}</span>
                {isSelected && <Check className="h-4 w-4 ml-2" />}
              </Button>
            );
          })}
        </div>
      </ScrollArea>

      {selectedSubjects.length > 0 && (
        <div className="mt-4">
          <Label>Your Selected Subjects</Label>
          <div className="flex flex-wrap gap-2 mt-2">
            {selectedSubjects.map((subject, index) => (
              <Button
                key={index}
                variant="secondary"
                className="flex items-center gap-2 px-3 py-1 h-auto"
                onClick={() => {
                  const subjectData = subjects.find(s => s.name === subject);
                  if (subjectData) {
                    handleSubjectToggle(subjectData.id, subject);
                  }
                }}
              >
                {subject}
                <X className="h-3 w-3" />
              </Button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};