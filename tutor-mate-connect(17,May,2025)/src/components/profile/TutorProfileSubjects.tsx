import { useEffect, useState } from "react";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { supabase } from "@/integrations/supabase/client";

type Props = {
  tutorId: string;
  selectedSubjects: string[];
  isEditing: boolean;
  onSubjectsChange: (subjects: string[]) => void;
};

export const TutorProfileSubjects = ({
  tutorId,
  selectedSubjects,
  isEditing,
  onSubjectsChange,
}: Props) => {
  const [allSubjects, setAllSubjects] = useState<string[]>([]);

  useEffect(() => {
    const fetchSubjects = async () => {
      const { data, error } = await supabase.from("supported_subjects").select("name");

      if (!error && data) {
        setAllSubjects(data.map((item) => item.name));
      }
    };
    fetchSubjects();
  }, []);

  const handleCheckboxChange = (subject: string) => {
    let updatedSubjects;
    if (selectedSubjects.includes(subject)) {
      updatedSubjects = selectedSubjects.filter((s) => s !== subject);
    } else {
      updatedSubjects = [...selectedSubjects, subject];
    }
    onSubjectsChange(updatedSubjects);
  };

  return (
    <div className="space-y-2">
      <Label className="text-lg font-semibold">Subjects</Label>

      {!isEditing ? (
        selectedSubjects.length > 0 ? (
          <ul className="list-disc pl-5 text-sm">
            {selectedSubjects.map((subject) => (
              <li key={subject}>{subject}</li>
            ))}
          </ul>
        ) : (
          <p className="text-sm text-muted-foreground">No subjects selected.</p>
        )
      ) : (
        <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
          {allSubjects.map((subject) => (
            <label key={subject} className="flex items-center space-x-2">
              <Checkbox
                checked={selectedSubjects.includes(subject)}
                onCheckedChange={() => handleCheckboxChange(subject)}
              />
              <span className="text-sm">{subject}</span>
            </label>
          ))}
        </div>
      )}
    </div>
  );
};
