import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardH<PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useToast } from "@/components/ui/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { StudentProfileFields } from "@/components/profile/StudentProfileFields";
import { StudentProfileBasicInfo } from "@/components/profile/StudentProfileBasicInfo";
import { StudentParentInfo } from "@/components/profile/StudentParentInfo";
import { ProfilePhotoUpload } from "./ProfilePhotoUpload";

export const StudentProfileView = () => {
  const { toast } = useToast();
  const [isEditing, setIsEditing] = useState(false);
  const [profileData, setProfileData] = useState<any>({});
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    fetchProfile();
  }, []);

  const fetchProfile = async () => {
    try {
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      if (userError) throw userError;
      if (!user) {
        toast({
          title: "Error",
          description: "No authenticated user found",
          variant: "destructive",
        });
        return;
      }
  
      
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', user.id)
        .eq('role', 'student') 
        .maybeSingle();
      
      if (profile) {
        setProfileData(profile);
        
        return; 
      }
  
      
      const { data: student, error: studentError } = await supabase
        .from('student_profiles')
        .select('*')
        .eq('id', user.id) 
        .single();
        
      if (student) {
        setProfileData(student);
        
        return;
      }
  
      
      toast({
        title: "Error",
        description: "Student profile not found",
        variant: "destructive",
      });
  
    } catch (error) {
      console.error('Error fetching profile:', error);
      toast({
        title: "Error",
        description: "Failed to load profile data",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };
  
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    if (name.startsWith('parent_info_')) {
      const field = name.replace('parent_info_', '');
      setProfileData({
        ...profileData,
        parent_info: {
          ...profileData.parent_info,
          [field]: value,
        },
      });
    } else {
      setProfileData({ ...profileData, [name]: value });
    }
  };

  const handlePhotoUpdate = async (url: string | null) => {
    setProfileData({ ...profileData, profile_photo_url: url });
  };

 
  const handleSelectChange = (field: string, value: string | string[]) => {
    setProfileData({ ...profileData, [field]: value });
  };

  const handleSave = async () => {
    try {
      const { data: { user }, error: userError } = await supabase.auth.getUser();
  
      if (userError) throw userError;
      if (!user) {
        toast({
          title: "Error",
          description: "No authenticated user found",
          variant: "destructive",
        });
        return;
      }
  
      
      const rawData = {
        full_name: profileData.full_name,
        phone_number: profileData.phone_number,
        location: profileData.location,
        timezone: profileData.timezone,
        profile_photo_url: profileData.profile_photo_url,
        grade_level: profileData.grade_level,
        subjects: profileData.subjects,
        learning_goals: profileData.learning_goals,
        learning_style: profileData.learning_style,
        preferred_languages: profileData.preferred_languages,
        subscriptions:profileData.subscriptions,
        special_needs: profileData.special_needs,
        parent_info: profileData.parent_info ?? {}, 
        updated_at: new Date().toISOString(),
      };
  
      
      const updateData = Object.fromEntries(
        Object.entries(rawData).filter(([_, value]) => value !== undefined)
      );
  
      
  
      const { data, error } = await supabase
        .from('student_profiles')
        .update(updateData)
        .eq('id', user.id);
  
      if (error) throw error;
  
      setIsEditing(false);
      toast({
        title: "Profile updated",
        description: "Your changes have been saved successfully.",
      });
    } catch (error) {
      console.error('Error saving profile:', error);
      toast({
        title: "Error",
        description: "Failed to save profile changes",
        variant: "destructive",
      });
    }
  };
  
  if (isLoading) {
    return (
      <Card>
        <CardContent className="p-6">
          Loading profile data...
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle className="text-2xl font-bold">Student Profile</CardTitle>
        <Button
          variant="outline"
          onClick={() => setIsEditing(!isEditing)}
        >
          {isEditing ? "Cancel" : "Edit Profile"}
        </Button>
      </CardHeader>
      <CardContent className="space-y-6">
        <ProfilePhotoUpload
          currentPhotoUrl={profileData.profile_photo_url}
          onPhotoUpdate={handlePhotoUpdate}
          isEditing={isEditing}
        />
        <StudentProfileBasicInfo
          profileData={profileData}
          handleChange={handleChange}
          isEditing={isEditing}
        />
        <StudentProfileFields
          profileData={profileData}
          handleChange={handleChange}
          handleSelectChange={handleSelectChange}
          isEditing={isEditing}
        />
        <StudentParentInfo
          profileData={profileData}
          handleChange={handleChange}
          isEditing={isEditing}
        />
        {isEditing && (
          <Button onClick={handleSave} className="w-full">
            Save Changes
          </Button>
        )}
      </CardContent>
    </Card>
  );
};