import { useProfile } from "@/contexts/ProfileContext";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Switch } from "@/components/ui/switch";
import { Button } from "@/components/ui/button";
import { Plus, X } from "lucide-react";

export const Availability = () => {
  const { profileData, setProfileData } = useProfile();

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setProfileData({ ...profileData, [e.target.name]: e.target.value });
  };

  const handleSwitchChange = (checked: boolean) => {
    setProfileData({ ...profileData, flexibleScheduling: checked });
  };

  const handleArrayChange = (field: string, value: string) => {
    if (!value.trim()) return;
    setProfileData({
      ...profileData,
      [field]: [...(profileData[field] || []), value],
    });
  };

  const removeArrayItem = (field: string, index: number) => {
    setProfileData({
      ...profileData,
      [field]: profileData[field].filter((_: string, i: number) => i !== index),
    });
  };

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h3 className="text-lg font-semibold">Availability & Scheduling</h3>
        <p className="text-sm text-muted-foreground">
          Be clear about your availability to avoid scheduling conflicts.
        </p>
      </div>

      <div className="space-y-4">
        <div>
          <Label>Available Time Slots</Label>
          <div className="space-y-2">
            <div className="flex gap-2">
              <Input
                placeholder="Add availability (e.g., Mon 9AM-5PM)"
                onKeyPress={(e) => {
                  if (e.key === 'Enter') {
                    e.preventDefault();
                    handleArrayChange('availability', e.currentTarget.value);
                    e.currentTarget.value = '';
                  }
                }}
              />
              <Button
                type="button"
                variant="outline"
                size="icon"
                onClick={(e) => {
                  const input = e.currentTarget.previousElementSibling as HTMLInputElement;
                  handleArrayChange('availability', input.value);
                  input.value = '';
                }}
              >
                <Plus className="h-4 w-4" />
              </Button>
            </div>
            <div className="flex flex-wrap gap-2">
              {profileData.availability?.map((slot: string, index: number) => (
                <div
                  key={index}
                  className="flex items-center gap-1 rounded-full bg-muted px-3 py-1 text-sm"
                >
                  {slot}
                  <button
                    type="button"
                    onClick={() => removeArrayItem('availability', index)}
                    className="ml-1 text-muted-foreground hover:text-foreground"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </div>
              ))}
            </div>
          </div>
        </div>

        <div>
          <Label htmlFor="sessionDuration">Session Duration</Label>
          <Input
            id="sessionDuration"
            name="sessionDuration"
            value={profileData.sessionDuration}
            onChange={handleChange}
            placeholder="e.g., 60 minutes"
          />
        </div>

        <div>
          <Label htmlFor="maxStudents">Maximum Students per Session</Label>
          <Input
            id="maxStudents"
            name="maxStudents"
            type="number"
            min="1"
            value={profileData.maxStudents}
            onChange={handleChange}
            placeholder="1"
          />
        </div>

        <div className="flex items-center justify-between">
          <Label htmlFor="flexibleScheduling">Flexible Scheduling</Label>
          <Switch
            id="flexibleScheduling"
            checked={profileData.flexibleScheduling}
            onCheckedChange={handleSwitchChange}
          />
        </div>
      </div>
    </div>
  );
};