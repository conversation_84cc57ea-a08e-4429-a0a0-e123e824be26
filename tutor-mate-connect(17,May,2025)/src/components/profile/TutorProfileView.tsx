import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button"
import { useToast } from "@/components/ui/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { TutorProfileBasicInfo } from "./TutorProfileBasicInfo";
import { TutorProfileProfessionalInfo } from "./TutorProfileProfessionalInfo";
import { ProfilePhotoUpload } from "./ProfilePhotoUpload";
export const TutorProfileView = () => {
  const { toast } = useToast();
  const [isEditing, setIsEditing] = useState(false);
  const [profileData, setProfileData] = useState<any>({});

  useEffect(() => {
    fetchTutorProfile();
  }, []);

  const fetchTutorProfile = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error("No user found");

      const { data: profileResult, error: profileError } = await supabase
        .from('tutor_profiles')
        .select('*')
        .eq('id', user.id)
        .maybeSingle();

      if (profileError) throw profileError;

      const { data: languagesData, error: languagesError } = await supabase
        .from('tutor_languages')
        .select(`
          language_id,
          supported_languages (
            name
          )
        `)
        .eq('tutor_id', user.id);

      if (languagesError) throw languagesError;

      const transformedData = {
        ...profileResult,
        languages_spoken: languagesData?.map(l => l.supported_languages.name) || []
      };

      setProfileData(transformedData || {});
    } catch (error) {
      console.error('Error fetching profile:', error);
      toast({
        title: "Error",
        description: "Failed to load profile data",
        variant: "destructive",
      });
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setProfileData({ ...profileData, [name]: value });
  };

  const handleArrayChange = (field: string) => (items: string[]) => {
    setProfileData({ ...profileData, [field]: items });
  };

  const handlePhotoUpdate = async (url: string | null) => {
    setProfileData({ ...profileData, profile_photo_url: url });
  };

  const handleSave = async () => {
    try {
      const { error: profileError } = await supabase
        .from('tutor_profiles')
        .update({
          full_name: profileData.full_name,
          phone_number: profileData.phone_number,
          location: profileData.location,
          calendly_username:profileData.calendly_username,
          timezone: profileData.timezone,
          bio: profileData.bio,
          teaching_experience: profileData.teaching_experience,
          teaching_style: profileData.teaching_style,
          hourly_rate: profileData.hourly_rate,
          profile_photo_url: profileData.profile_photo_url,
          subjects: profileData.subjects,
          updated_at: new Date().toISOString(),
        })
        .eq('id', profileData.id);

      if (profileError) throw profileError;

      
      if (profileData.languages_spoken) {
        const { data: languageIds, error: languagesLookupError } = await supabase
          .from('supported_languages')
          .select('id, name')
          .in('name', profileData.languages_spoken);

        if (languagesLookupError) throw languagesLookupError;

        await supabase
          .from('tutor_languages')
          .delete()
          .eq('tutor_id', profileData.id);

        if (languageIds && languageIds.length > 0) {
          const languageData = languageIds.map(language => ({
            tutor_id: profileData.id,
            language_id: language.id
          }));

          const { error: languagesError } = await supabase
            .from('tutor_languages')
            .insert(languageData);

          if (languagesError) throw languagesError;
        }
      }

      setIsEditing(false);
      toast({
        title: "Profile updated",
        description: "Your changes have been saved successfully.",
      });

      fetchTutorProfile();
    } catch (error) {
      console.error('Error saving profile:', error);
      toast({
        title: "Error",
        description: "Failed to save profile changes",
        variant: "destructive",
      });
    }
  };

  

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle className="text-2xl font-bold">Tutor Profile</CardTitle>
        <Button
          variant="outline"
          onClick={() => setIsEditing(!isEditing)}
        >
          {isEditing ? "Cancel" : "Edit Profile"}
        </Button>
      </CardHeader>
      <CardContent className="space-y-6">
        <ProfilePhotoUpload
          currentPhotoUrl={profileData.profile_photo_url}
          onPhotoUpdate={handlePhotoUpdate}
          isEditing={isEditing}
        />
      

        <TutorProfileBasicInfo
          profileData={profileData}
          isEditing={isEditing}
          handleChange={handleChange}
        />
        <TutorProfileProfessionalInfo
          profileData={profileData}
          isEditing={isEditing}
          handleChange={handleChange}
          handleArrayChange={handleArrayChange}
        />
      

        {isEditing && (
          <Button onClick={handleSave} className="w-full">
            Save Changes
          </Button>
        )}
      </CardContent>
    </Card>
  );
};
