import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

interface TutorProfileBasicInfoProps {
  profileData: any;
  isEditing: boolean;
  handleChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => void;
}

export const TutorProfileBasicInfo = ({
  profileData,
  isEditing,
  handleChange,
}: TutorProfileBasicInfoProps) => {
  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold">Basic Information</h3>
      <div className="grid gap-4 sm:grid-cols-2">
        <div className="space-y-2">
          <Label htmlFor="full_name">Full Name</Label>
          <Input
            id="full_name"
            name="full_name"
            value={profileData.full_name || ''}
            onChange={handleChange}
            readOnly={!isEditing}
            className={!isEditing ? "bg-gray-100" : ""}
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="email">Email</Label>
          <Input
            id="email"
            value={profileData.email || ''}
            readOnly
            className="bg-gray-100"
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="phone_number">Phone</Label>
          <Input
            id="phone_number"
            name="phone_number"
            value={profileData.phone_number || ''}
            onChange={handleChange}
            readOnly={!isEditing}
            className={!isEditing ? "bg-gray-100" : ""}
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="location">Location</Label>
          <Input
            id="location"
            name="location"
            value={profileData.location || ''}
            onChange={handleChange}
            readOnly={!isEditing}
            className={!isEditing ? "bg-gray-100" : ""}
          />
        </div>
        <div className="space-y-2">
  <Label htmlFor="calendly_username">Calendly Username</Label>
  <Input
    id="calendly_username"
    name="calendly_username"
    value={profileData.calendly_username || ''}
    onChange={handleChange}
    readOnly={!isEditing}
    className={!isEditing ? "bg-gray-100" : ""}
    placeholder="e.g., yourname"
  />
</div>
        <div className="space-y-2">
          <Label htmlFor="timezone">Timezone</Label>
          <Input
            id="timezone"
            name="timezone"
            value={profileData.timezone || ''}
            onChange={handleChange}
            readOnly={!isEditing}
            className={!isEditing ? "bg-gray-100" : ""}
          />
        </div>
      </div>
    </div>
  );
};