import { Label } from "@/components/ui/label";

interface ArrayDisplayProps {
  label: string;
  items: string[];
  displayNames?: Record<string, string>;
  onRemove?: (index: number) => void;
  isEditing?: boolean;
}

export const ArrayDisplay = ({
  label,
  items = [],
  displayNames = {},
  onRemove,
  isEditing = false,
}: ArrayDisplayProps) => {
  if (!items || items.length === 0) {
    return (
      <div className="space-y-2">
        <Label>{label}</Label>
        <div className="text-sm text-muted-foreground">No {label.toLowerCase()} selected</div>
      </div>
    );
  }
  return (
    <div className="space-y-2">
      <Label>{label}</Label>
      <div className="flex flex-wrap gap-2">
        {items.map((item, index) => (
          <span
            key={index}
            className="inline-flex items-center bg-muted px-3 py-1 rounded-full text-sm"
          >
            {displayNames[item] || item}
            {isEditing && onRemove && (
              <button
                onClick={() => onRemove(index)}
                className="ml-2 text-muted-foreground hover:text-foreground"
              >
                ×
              </button>
            )}
          </span>
        ))}
      </div>
    </div>
  );
};