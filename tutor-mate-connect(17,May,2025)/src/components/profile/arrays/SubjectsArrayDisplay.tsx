
import { useEffect, useState } from "react";
import { supabase } from "@/integrations/supabase/client";
import { SubjectsSelect } from "../professional/SubjectsSelect";
import { ArrayDisplay } from "./ArrayDisplay";

interface SubjectsArrayDisplayProps {
  items?: string[] | null;
  isEditing?: boolean;
  onChange?: (items: string[]) => void;
}

export const SubjectsArrayDisplay = ({
  items = [],
  isEditing = false,
  onChange,
}: SubjectsArrayDisplayProps) => {
  const [displayNames, setDisplayNames] = useState<Record<string, string>>({});

  useEffect(() => {
    const fetchDisplayNames = async () => {
      if (!items?.length) return;

      const { data, error } = await supabase
        .from("subjects")
        .select("name")
        .in("name", items);

      if (!error && data) {
        const names = data.reduce((acc, curr) => {
          acc[curr.name] = curr.name;
          return acc;
        }, {} as Record<string, string>);
        setDisplayNames(names);
      }
    };

    fetchDisplayNames();
  }, [items]);
  if (isEditing) {
    return (
      <div className="space-y-2">
        <SubjectsSelect
          selectedSubjects={items || []}
          onSubjectSelect={(value) => onChange?.([...(items || []), value])}
          onSubjectRemove={(index) =>
            onChange?.(items?.filter((_, i) => i !== index) || [])
          }
        />
      </div>
    );
  }

  return (
    <ArrayDisplay
      label="Subjects"
      items={items || []}
      displayNames={displayNames}
      isEditing={isEditing}
      onRemove={
        onChange
          ? (index) => onChange(items?.filter((_, i) => i !== index) || [])
          : undefined
      }
    />
  );
};
