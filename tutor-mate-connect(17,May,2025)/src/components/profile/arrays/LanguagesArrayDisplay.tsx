import { useEffect, useState } from "react";
import { supabase } from "@/integrations/supabase/client";
import { LanguagesSelect } from "../professional/LanguagesSelect";
import { ArrayDisplay } from "./ArrayDisplay";

interface LanguagesArrayDisplayProps {
  items?: string[] | null;
  isEditing?: boolean;
  onChange?: (items: string[]) => void;
}

export const LanguagesArrayDisplay = ({
  items = [],
  isEditing = false,
  onChange,
}: LanguagesArrayDisplayProps) => {
  const [displayNames, setDisplayNames] = useState<Record<string, string>>({});

  useEffect(() => {
    const fetchDisplayNames = async () => {
      if (!items?.length) return;
      
      const { data, error } = await supabase
        .from('supported_languages')
        .select('name')
        .in('name', items);
        
      if (!error && data) {
        const names = data.reduce((acc, curr) => ({
          ...acc,
          [curr.name]: curr.name
        }), {});
        setDisplayNames(names);
      }
    };

    fetchDisplayNames();
  }, [items]);

  if (isEditing) {
    return (
      <div className="space-y-2">
        <LanguagesSelect
          selectedLanguages={items || []}
          onLanguageSelect={(value) => onChange?.([...(items || []), value])}
          onLanguageRemove={(index) => 
            onChange?.(items?.filter((_, i) => i !== index) || [])
          }
        />
      </div>
    );
  }

  return (
    <ArrayDisplay
      label="Languages"
      items={items || []}
      displayNames={displayNames}
      isEditing={isEditing}
      onRemove={
        onChange ? 
        (index) => onChange(items?.filter((_, i) => i !== index) || []) 
        : undefined
      }
    />
  );
};