import { useProfile } from "@/contexts/ProfileContext";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Upload } from "lucide-react";

export const Verification = () => {
  const { profileData, setProfileData, uploadFile } = useProfile();

  const handleFileUpload = async (e: React.ChangeEvent<HTMLInputElement>, field: string) => {
    if (!e.target.files || e.target.files.length === 0) return;
    const file = e.target.files[0];

    

    
    const publicUrl = await uploadFile(file, field);

    if (publicUrl) {
      setProfileData((prev: any) => ({
        ...prev,
        verification_documents: {
          ...prev.verification_documents,
          [field]: publicUrl,
        },
      }));
    }
  };

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h3 className="text-lg font-semibold">Verification & Documents</h3>
        <p className="text-sm text-muted-foreground">
          Verification helps build trust with students and increases your booking chances.
        </p>
      </div>

      <div className="space-y-4">
        <div>
          <Label htmlFor="certifications">Upload Certifications</Label>
          <div className="mt-1">
            <Button variant="outline" className="w-full relative">
              <Upload className="mr-2 h-4 w-4" />
              Upload Certificates
              <input
                type="file"
                className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                onChange={(e) => handleFileUpload(e, "certifications")}
                accept=".pdf,.doc,.docx"
                multiple={false}
              />
            </Button>
          </div>
        </div>

        <div>
          <Label htmlFor="backgroundCheck">Background Check</Label>
          <div className="mt-1">
            <Button variant="outline" className="w-full relative">
              <Upload className="mr-2 h-4 w-4" />
              Upload Background Check
              <input
                type="file"
                className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                onChange={(e) => handleFileUpload(e, "background_check")}
                accept=".pdf,.doc,.docx"
              />
            </Button>
          </div>
        </div>

        <div>
          <Label htmlFor="idVerification">ID Verification</Label>
          <div className="mt-1">
            <Button variant="outline" className="w-full relative">
              <Upload className="mr-2 h-4 w-4" />
              Upload ID Document
              <input
                type="file"
                className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                onChange={(e) => handleFileUpload(e, "id_verification")}
                accept=".pdf,.jpg,.jpeg,.png"
              />
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};
