import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { SubjectsArrayDisplay } from "./arrays/SubjectsArrayDisplay";
import { LanguagesArrayDisplay } from "./arrays/LanguagesArrayDisplay";

interface TutorProfileArrayDisplayProps {
  label: string;
  items?: string[] | null;
  isEditing?: boolean;
  onChange?: (items: string[]) => void;
  type?: 'subjects' | 'languages' | 'default';
}

export const TutorProfileArrayDisplay = ({
  label,
  items,
  isEditing = false,
  onChange,
  type = 'default'
}: TutorProfileArrayDisplayProps) => {
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (onChange) {
      const newItems = e.target.value.split(',').map(item => item.trim()).filter(item => item !== '');
      onChange(newItems);
    }
  };

  if (type === 'subjects') {
    return (
      <div className="space-y-4">
        <SubjectsArrayDisplay
          items={items}
          isEditing={isEditing}
          onChange={onChange}
        />
      </div>
    );
  }

  if (type === 'languages') {
    return (
      <div className="space-y-4">
        <LanguagesArrayDisplay
          items={items}
          isEditing={isEditing}
          onChange={onChange}
        />
      </div>
    );
  }

  return (
    <div className="space-y-2">
      <Label>{label}</Label>
      {isEditing ? (
        <Input
          value={items?.join(', ') || ''}
          onChange={handleInputChange}
          placeholder={`Enter ${label.toLowerCase()} separated by commas`}
        />
      ) : (
        <div className="flex flex-wrap gap-2">
          {items?.map((item, index) => (
            <span
              key={index}
              className="inline-block bg-muted px-3 py-1 rounded-full text-sm"
            >
              {item}
            </span>
          ))}
        </div>
      )}
    </div>
  );
};