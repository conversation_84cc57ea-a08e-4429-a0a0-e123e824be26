import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { TutorProfileArrayDisplay } from "./TutorProfileArrayDisplay";

interface TutorProfileProfessionalInfoProps {
  profileData: any;
  isEditing: boolean;
  handleChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => void;
  handleArrayChange?: (field: string) => (items: string[]) => void;
}

export const TutorProfileProfessionalInfo = ({
  profileData,
  isEditing,
  handleChange,
  handleArrayChange,
}: TutorProfileProfessionalInfoProps) => {
  
  return (
    <div className="space-y-6">
      <div className="space-y-4">
        <Label>Professional Information</Label>
        <Textarea
          name="bio"
          value={profileData.bio || ''}
          onChange={handleChange}
          placeholder="Tell us about yourself..."
          disabled={!isEditing}
          className="min-h-[100px]"
        />
      </div>

      <div className="space-y-4">
        <Label>Teaching Experience</Label>
        <Textarea
          name="teaching_experience"
          value={profileData.teaching_experience || ''}
          onChange={handleChange}
          placeholder="Describe your teaching experience..."
          disabled={!isEditing}
        />
      </div>

      <div className="space-y-4">
        <Label>Teaching Style</Label>
        <Textarea
          name="teaching_style"
          value={profileData.teaching_style || ''}
          onChange={handleChange}
          placeholder="Describe your teaching style..."
          disabled={!isEditing}
        />
      </div>

      <div className="space-y-4">
        <TutorProfileArrayDisplay
          label="Subjects"
          items={profileData.subjects}
          isEditing={isEditing}
          onChange={handleArrayChange?.('subjects')}
          type="subjects"
        />
      </div>

      <div className="space-y-4">
        <TutorProfileArrayDisplay
          label="Languages"
          items={profileData.languages_spoken}
          isEditing={isEditing}
          onChange={handleArrayChange?.('languages_spoken')}
          type="languages"
        />
      </div>

      <div className="space-y-2">
        <Label>Hourly Rate ($)</Label>
        <Input
          type="number"
          name="hourly_rate"
          value={profileData.hourly_rate || ''}
          onChange={handleChange}
          placeholder="Enter your hourly rate"
          disabled={!isEditing}
        />
      </div>
    </div>
  );
};