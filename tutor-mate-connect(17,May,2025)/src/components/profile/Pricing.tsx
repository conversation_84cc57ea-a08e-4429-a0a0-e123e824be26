import { useProfile } from "@/contexts/ProfileContext";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Plus, X } from "lucide-react";

export const Pricing = () => {
  const { profileData, setProfileData } = useProfile();

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setProfileData({ ...profileData, [e.target.name]: e.target.value });
  };

  const handleArrayChange = (field: string, value: string) => {
    if (!value.trim()) return;
    setProfileData({
      ...profileData,
      [field]: [...(profileData[field] || []), value],
    });
  };

  const removeArrayItem = (field: string, index: number) => {
    setProfileData({
      ...profileData,
      [field]: profileData[field].filter((_: string, i: number) => i !== index),
    });
  };

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h3 className="text-lg font-semibold">Pricing & Payment</h3>
        <p className="text-sm text-muted-foreground">
          Set fair and competitive rates to attract more students.
        </p>
      </div>

      <div className="space-y-4">
        <div>
          <Label htmlFor="hourlyRate">Hourly Rate ($)</Label>
          <Input
            id="hourlyRate"
            name="hourlyRate"
            type="number"
            min="0"
            step="0.01"
            value={profileData.hourlyRate}
            onChange={handleChange}
            placeholder="30.00"
          />
        </div>

        <div>
          <Label>Session Packages</Label>
          <div className="space-y-2">
            <div className="flex gap-2">
              <Input
                placeholder="Add a package (e.g., 5 sessions for $135)"
                onKeyPress={(e) => {
                  if (e.key === 'Enter') {
                    e.preventDefault();
                    handleArrayChange('sessionPackages', e.currentTarget.value);
                    e.currentTarget.value = '';
                  }
                }}
              />
              <Button
                type="button"
                variant="outline"
                size="icon"
                onClick={(e) => {
                  const input = e.currentTarget.previousElementSibling as HTMLInputElement;
                  handleArrayChange('sessionPackages', input.value);
                  input.value = '';
                }}
              >
                <Plus className="h-4 w-4" />
              </Button>
            </div>
            <div className="flex flex-wrap gap-2">
              {profileData.sessionPackages?.map((pkg: string, index: number) => (
                <div
                  key={index}
                  className="flex items-center gap-1 rounded-full bg-muted px-3 py-1 text-sm"
                >
                  {pkg}
                  <button
                    type="button"
                    onClick={() => removeArrayItem('sessionPackages', index)}
                    className="ml-1 text-muted-foreground hover:text-foreground"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </div>
              ))}
            </div>
          </div>
        </div>

        <div>
          <Label htmlFor="paymentMethod">Preferred Payment Method</Label>
          <Input
            id="paymentMethod"
            name="paymentMethod"
            value={profileData.paymentMethod}
            onChange={handleChange}
            placeholder="e.g., Credit Card, PayPal"
          />
        </div>
      </div>
    </div>
  );
};