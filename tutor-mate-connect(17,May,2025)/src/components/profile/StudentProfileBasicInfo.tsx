import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";

type StudentProfileBasicInfoProps = {
  profileData: any;
  isEditing: boolean;
  handleChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => void;
};

export const StudentProfileBasicInfo = ({
  profileData,
  isEditing,
  handleChange,
}: StudentProfileBasicInfoProps) => {
  return (
    <div className="space-y-8">
      {/* Basic Information */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Basic Information</h3>
        <div className="grid gap-4 sm:grid-cols-2">
          <div className="space-y-2">
            <Label htmlFor="full_name">Full Name</Label>
            <Input
              id="full_name"
              value={profileData.full_name || ''}
              readOnly
              className="bg-gray-100"
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="email">Email</Label>
            <Input
              id="email"
              value={profileData.email || ''}
              readOnly
              className="bg-gray-100"
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="phone_number">Phone</Label>
            <Input
              id="phone_number"
              name="phone_number"
              value={profileData.phone_number || ''}
              onChange={handleChange}
              readOnly={!isEditing}
              className={!isEditing ? "bg-gray-100" : ""}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="location">Location</Label>
            <Input
              id="location"
              name="location"
              value={profileData.location || ''}
              onChange={handleChange}
              readOnly={!isEditing}
              className={!isEditing ? "bg-gray-100" : ""}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="timezone">Timezone</Label>
            <Input
              id="timezone"
              name="timezone"
              value={profileData.timezone || ''}
              onChange={handleChange}
              readOnly={!isEditing}
              className={!isEditing ? "bg-gray-100" : ""}
            />
          </div>
        </div>
      </div>

      {/* Current Subscription */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Current Subscription</h3>
        <div className="grid gap-4 sm:grid-cols-3">
          <div className="space-y-2">
            <Label htmlFor="current_plan">Plan Name</Label>
            <Input
              id="current_plan"
              value={profileData.current_plan || 'No Plan'}
              readOnly
              className="bg-gray-100"
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="plan_status">Status</Label>
            <Input
              id="plan_status"
              value={profileData.plan_status || 'Inactive'}
              readOnly
              className="bg-gray-100"
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="expiry_date">Next Renewal</Label>
            <Input
              id="expiry_date"
              value={profileData.expiry_date || 'N/A'}
              readOnly
              className="bg-gray-100"
            />
          </div>
        </div>
      </div>
    </div>
  );
};
