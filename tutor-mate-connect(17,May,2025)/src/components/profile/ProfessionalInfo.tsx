import { useProfile } from "@/contexts/ProfileContext";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { Plus, X } from "lucide-react";
import { SubjectsSelect } from "./professional/SubjectsSelect";
import { LanguagesSelect } from "./professional/LanguagesSelect";

export const ProfessionalInfo = () => {
  const { profileData, setProfileData } = useProfile();

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setProfileData({ ...profileData, [e.target.name]: e.target.value });
  };

  const handleArrayChange = (field: string, value: string) => {
    if (!value.trim()) return;
    setProfileData({
      ...profileData,
      [field]: [...(profileData[field] || []), value],
    });
  };

  const removeArrayItem = (field: string, index: number) => {
    setProfileData({
      ...profileData,
      [field]: profileData[field].filter((_: string, i: number) => i !== index),
    });
  };

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h3 className="text-lg font-semibold">Professional Details & Expertise</h3>
        <p className="text-sm text-muted-foreground">
          Highlight what makes you unique! Share your teaching style and areas of expertise.
        </p>
      </div>

      <div className="space-y-4">
        <div>
          <Label htmlFor="bio">Bio/About Me</Label>
          <Textarea
            id="bio"
            name="bio"
            value={profileData.bio}
            onChange={handleChange}
            placeholder="Write a short, engaging introduction about yourself..."
            className="h-32"
          />
        </div>

        <div>
          <Label>Qualifications</Label>
          <div className="space-y-2">
            <div className="flex gap-2">
              <Input
                placeholder="Add a qualification"
                onKeyPress={(e) => {
                  if (e.key === 'Enter') {
                    e.preventDefault();
                    handleArrayChange('qualifications', e.currentTarget.value);
                    e.currentTarget.value = '';
                  }
                }}
              />
              <Button
                type="button"
                variant="outline"
                size="icon"
                onClick={(e) => {
                  const input = e.currentTarget.previousElementSibling as HTMLInputElement;
                  handleArrayChange('qualifications', input.value);
                  input.value = '';
                }}
              >
                <Plus className="h-4 w-4" />
              </Button>
            </div>
            <div className="flex flex-wrap gap-2">
              {profileData.qualifications?.map((qual: string, index: number) => (
                <div
                  key={index}
                  className="flex items-center gap-1 rounded-full bg-muted px-3 py-1 text-sm"
                >
                  {qual}
                  <button
                    type="button"
                    onClick={() => removeArrayItem('qualifications', index)}
                    className="ml-1 text-muted-foreground hover:text-foreground"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </div>
              ))}
            </div>
          </div>
        </div>

        <div>
          <Label htmlFor="experience">Teaching Experience</Label>
          <Textarea
            id="experience"
            name="experience"
            value={profileData.experience}
            onChange={handleChange}
            placeholder="Describe your teaching experience..."
          />
        </div>

        <SubjectsSelect
          selectedSubjects={profileData.subjects || []}
          onSubjectSelect={(value) => handleArrayChange('subjects', value)}
          onSubjectRemove={(index) => removeArrayItem('subjects', index)}
        />

        <LanguagesSelect
          selectedLanguages={profileData.languages_spoken || []}
          onLanguageSelect={(value) => handleArrayChange('languages_spoken', value)}
          onLanguageRemove={(index) => removeArrayItem('languages_spoken', index)}
        />

        <div>
          <Label htmlFor="teachingStyle">Teaching Style</Label>
          <Input
            id="teachingStyle"
            name="teachingStyle"
            value={profileData.teachingStyle}
            onChange={handleChange}
            placeholder="Describe your teaching style (e.g., Interactive, Structured, etc.)"
          />
        </div>
      </div>
    </div>
  );
};