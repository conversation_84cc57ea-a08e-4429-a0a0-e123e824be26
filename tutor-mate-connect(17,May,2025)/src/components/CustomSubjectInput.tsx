import { useState } from "react";
import { supabase } from "@/integrations/supabase/client";

export default function CustomSubjectInput({ tutorId }: { tutorId: string }) {
  const [subjectName, setSubjectName] = useState("");
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState("");

  const handleAddSubject = async () => {
    if (!subjectName.trim()) return;

    setLoading(true);
    setMessage("");

    try {
      
      const { data: existingSubject, error: findError } = await supabase
        .from("subjects")
        .select("*")
        .eq("name", subjectName.trim())
        .single();

      let subjectId;

      if (existingSubject) {
        subjectId = existingSubject.id;
      } else {
        
        const { data: newSubject, error: insertError } = await supabase
          .from("subjects")
          .insert({ name: subjectName.trim() })
          .select()
          .single();

        if (insertError) throw insertError;
        subjectId = newSubject.id;
      }

      
      const { error: linkError } = await supabase
        .from("subjects_tutor")
        .insert({ tutor_id: tutorId, subject_id: subjectId });

      if (linkError) throw linkError;

      setMessage("Subject added successfully!");
      setSubjectName(""); // clear input
    } catch (error: any) {
      console.error(error.message);
      setMessage("Error: " + error.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-2">
      <input
        type="text"
        value={subjectName}
        onChange={(e) => setSubjectName(e.target.value)}
        placeholder="Enter subject"
        className="border rounded px-3 py-2 w-full"
      />
      <button
        onClick={handleAddSubject}
        disabled={loading}
        className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
      >
        {loading ? "Adding..." : "Add Subject"}
      </button>
      {message && <p className="text-sm text-gray-700">{message}</p>}
    </div>
  );
}
