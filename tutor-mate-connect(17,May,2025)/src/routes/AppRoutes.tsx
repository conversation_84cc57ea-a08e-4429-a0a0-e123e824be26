import { Routes, Route, Navigate } from "react-router-dom";
import Index from "@/pages/Index";
import About from "@/pages/About";
import Login from "@/pages/Login";
import Contact from "@/pages/Contact";
import Resources from "@/pages/Resources";
import FindTutor from "@/pages/FindTutor";
import BecomeTutor from "@/pages/BecomeTutor";
import StudentDashboard from "@/pages/StudentDashboard";
import TutorDashboard from "@/pages/TutorDashboard";
import StudentSignup from "@/pages/StudentSignup";
import TutorSignup from "@/pages/TutorSignup";
import BuildProfile from "@/pages/BuildProfile";
import Subscriptions from "@/pages/Subscriptions";
import BuildStudentProfile from "@/pages/BuildStudentProfile";
import Profile from "@/pages/Profile";
import Subjects from "@/pages/Subjects";
import MessageCenter from "@/pages/MessageCenter";
import Subscription from "@/pages/Subscription";
import Privacy from "@/pages/Privacy";
import Terms from "@/pages/Terms";
import Careers from "@/pages/about/Careers";
import Press from "@/pages/about/Press";
import { ProtectedRoute } from "./ProtectedRoute";
import AuthCallback from "@/components/auth/authcallback";

export const AppRoutes = () => {
  return (
    <Routes>
      <Route path="/" element={<Index />} />
      <Route path="/about" element={<About />} />
      <Route path="/about/careers" element={<Careers />} />
      <Route path="/about/press" element={<Press />} />
      <Route path="/contact" element={<Contact />} />
      <Route path="/subscriptions" element={<Subscriptions/>} />
      <Route path="/resources" element={<Resources />} />
      <Route path="/find-tutor" element={<FindTutor />} />
      <Route path="/become-tutor" element={<BecomeTutor />} />
      <Route path="/login" element={<Login />} />
      <Route path="/subjects" element={<Subjects />} />
      <Route path="/privacy" element={<Privacy />} />
      <Route path="/terms" element={<Terms />} />
      //Signup pages moved to public route from protected route
      <Route path="/student-signup" element={<StudentSignup />} />
      <Route path="/tutor-signup" element={<TutorSignup />} />
      <Route path="/auth/v1/callback" element={<AuthCallback/>} />
      
      
      <Route
        path="/student-dashboard"
        element={
          <ProtectedRoute>
            <StudentDashboard />
          </ProtectedRoute>
        }
      />
      <Route
        path="/tutor-dashboard"
        element={
          <ProtectedRoute>
            <TutorDashboard />
          </ProtectedRoute>
        }
      />
      <Route
        path="/build-profile"
        element={
          <ProtectedRoute>
            <BuildProfile />
          </ProtectedRoute>
        }
      />
      <Route
        path="/build-student-profile"
        element={
          <ProtectedRoute>
            <BuildStudentProfile />
          </ProtectedRoute>
        }
      />
      <Route
        path="/profile"
        element={
          <ProtectedRoute>
            <Profile />
          </ProtectedRoute>
        }
      />
      <Route
        path="/messages"
        element={
          <ProtectedRoute>
            <MessageCenter />
          </ProtectedRoute>
        }
      />
      <Route
        path="/subscription"
        element={
          <ProtectedRoute>
            <Subscription />
          </ProtectedRoute>
        }
      />
      
      
      <Route path="*" element={<Navigate to="/" replace />} />
    </Routes>
  );
};
