{"version": "4", "remote": {"https://deno.land/std@0.168.0/async/abortable.ts": "80b2ac399f142cc528f95a037a7d0e653296352d95c681e284533765961de409", "https://deno.land/std@0.168.0/async/deadline.ts": "2c2deb53c7c28ca1dda7a3ad81e70508b1ebc25db52559de6b8636c9278fd41f", "https://deno.land/std@0.168.0/async/debounce.ts": "60301ffb37e730cd2d6f9dadfd0ecb2a38857681bd7aaf6b0a106b06e5210a98", "https://deno.land/std@0.168.0/async/deferred.ts": "77d3f84255c3627f1cc88699d8472b664d7635990d5358c4351623e098e917d6", "https://deno.land/std@0.168.0/async/delay.ts": "5a9bfba8de38840308a7a33786a0155a7f6c1f7a859558ddcec5fe06e16daf57", "https://deno.land/std@0.168.0/async/mod.ts": "7809ad4bb223e40f5fdc043e5c7ca04e0e25eed35c32c3c32e28697c553fa6d9", "https://deno.land/std@0.168.0/async/mux_async_iterator.ts": "770a0ff26c59f8bbbda6b703a2235f04e379f73238e8d66a087edc68c2a2c35f", "https://deno.land/std@0.168.0/async/pool.ts": "6854d8cd675a74c73391c82005cbbe4cc58183bddcd1fbbd7c2bcda42b61cf69", "https://deno.land/std@0.168.0/async/retry.ts": "e8e5173623915bbc0ddc537698fa418cf875456c347eda1ed453528645b42e67", "https://deno.land/std@0.168.0/async/tee.ts": "3a47cc4e9a940904fd4341f0224907e199121c80b831faa5ec2b054c6d2eff5e", "https://deno.land/std@0.168.0/http/server.ts": "e99c1bee8a3f6571ee4cdeb2966efad465b8f6fe62bec1bdb59c1f007cc4d155", "https://deno.land/std@0.217.0/async/delay.ts": "8e1d18fe8b28ff95885e2bc54eccec1713f57f756053576d8228e6ca110793ad", "https://deno.land/std@0.217.0/http/server.ts": "6dce295abc169d0956ae00432441331b3425afad4d79e8b3475739be2f04d614"}, "workspace": {"packageJson": {"dependencies": ["npm:@calcom/embed-react@^1.3.0", "npm:@eslint/js@^9.9.0", "npm:@hookform/resolvers@^3.9.0", "npm:@radix-ui/react-accordion@^1.2.0", "npm:@radix-ui/react-alert-dialog@^1.1.1", "npm:@radix-ui/react-aspect-ratio@^1.1.0", "npm:@radix-ui/react-avatar@^1.1.0", "npm:@radix-ui/react-checkbox@^1.1.1", "npm:@radix-ui/react-collapsible@^1.1.0", "npm:@radix-ui/react-context-menu@^2.2.1", "npm:@radix-ui/react-dialog@^1.1.2", "npm:@radix-ui/react-dropdown-menu@^2.1.1", "npm:@radix-ui/react-hover-card@^1.1.1", "npm:@radix-ui/react-label@^2.1.0", "npm:@radix-ui/react-menubar@^1.1.1", "npm:@radix-ui/react-navigation-menu@^1.2.0", "npm:@radix-ui/react-popover@^1.1.1", "npm:@radix-ui/react-progress@^1.1.0", "npm:@radix-ui/react-radio-group@^1.2.0", "npm:@radix-ui/react-scroll-area@^1.1.0", "npm:@radix-ui/react-select@^2.1.1", "npm:@radix-ui/react-separator@^1.1.0", "npm:@radix-ui/react-slider@^1.2.0", "npm:@radix-ui/react-slot@^1.1.0", "npm:@radix-ui/react-switch@^1.1.0", "npm:@radix-ui/react-tabs@^1.1.0", "npm:@radix-ui/react-toast@^1.2.1", "npm:@radix-ui/react-toggle-group@^1.1.0", "npm:@radix-ui/react-toggle@^1.1.0", "npm:@radix-ui/react-tooltip@^1.1.4", "npm:@reduxjs/toolkit@^2.5.1", "npm:@supabase/auth-helpers-react@0.5", "npm:@supabase/auth-ui-react@~0.4.7", "npm:@supabase/auth-ui-shared@~0.1.8", "npm:@supabase/supabase-js@^2.47.10", "npm:@tailwindcss/typography@~0.5.15", "npm:@tanstack/react-query@^5.56.2", "npm:@types/node@^22.5.5", "npm:@types/react-dom@^18.3.0", "npm:@types/react@^18.3.3", "npm:@vitejs/plugin-react-swc@^3.5.0", "npm:autoprefixer@^10.4.20", "npm:class-variance-authority@~0.7.1", "npm:clsx@^2.1.1", "npm:cmdk@1", "npm:date-fns@^3.6.0", "npm:embla-carousel-react@^8.5.1", "npm:eslint-plugin-react-hooks@^5.1.0-rc.0", "npm:eslint-plugin-react-refresh@~0.4.9", "npm:eslint@^9.9.0", "npm:globals@^15.9.0", "npm:input-otp@^1.2.4", "npm:lovable-tagger@^1.0.19", "npm:lucide-react@0.462", "npm:module@^1.2.5", "npm:modules@0.4", "npm:next-themes@0.3", "npm:node@^23.8.0", "npm:postcss@^8.4.47", "npm:react-day-picker@^8.10.1", "npm:react-dom@^18.3.1", "npm:react-hook-form@^7.53.0", "npm:react-resizable-panels@^2.1.3", "npm:react-router-dom@^6.26.2", "npm:react@^18.3.1", "npm:recharts@^2.12.7", "npm:sonner@^1.5.0", "npm:tailwind-merge@^2.5.2", "npm:tailwindcss-animate@^1.0.7", "npm:tailwindcss@^3.4.11", "npm:typescript-eslint@^8.0.1", "npm:typescript@^5.5.3", "npm:vaul@~0.9.3", "npm:vite@^5.4.1", "npm:zod@^3.23.8"]}}}